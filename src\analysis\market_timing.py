"""
Market Timing and Session Analysis Module
Provides advanced market timing analysis for optimal trading windows
"""

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.config import MarketTimingConfig

logger = get_logger(__name__)


class TradingSession(Enum):
    """Trading session types"""
    ASIAN = "asian"
    LONDON = "london"
    NEW_YORK = "new_york"
    OVERLAP_LONDON_NY = "overlap_london_ny"
    OVERLAP_ASIAN_LONDON = "overlap_asian_london"
    OFF_HOURS = "off_hours"


@dataclass
class SessionInfo:
    """Trading session information"""
    session: TradingSession
    start_time: time
    end_time: time
    timezone: str
    volatility_factor: float
    volume_factor: float
    spread_factor: float


@dataclass
class MarketCondition:
    """Current market condition analysis"""
    current_session: TradingSession
    session_overlap: bool
    volatility_level: str  # 'low', 'medium', 'high'
    volume_level: str  # 'low', 'medium', 'high'
    spread_condition: str  # 'tight', 'normal', 'wide'
    trading_score: float  # 0-1, higher is better for trading
    recommendations: List[str]


@dataclass
class NewsImpact:
    """News impact analysis"""
    has_major_news: bool
    news_time: Optional[datetime]
    impact_level: str  # 'low', 'medium', 'high'
    affected_currencies: List[str]
    avoid_trading: bool


class MarketTimingAnalyzer:
    """Advanced market timing and session analysis"""

    def __init__(self, config: MarketTimingConfig):
        self.config = config

        # Define trading sessions (UTC times)
        self.sessions = {
            TradingSession.ASIAN: SessionInfo(
                TradingSession.ASIAN, time(23, 0), time(8, 0), "UTC",
                volatility_factor=0.7, volume_factor=0.6, spread_factor=1.2
            ),
            TradingSession.LONDON: SessionInfo(
                TradingSession.LONDON, time(8, 0), time(17, 0), "UTC",
                volatility_factor=1.0, volume_factor=1.0, spread_factor=1.0
            ),
            TradingSession.NEW_YORK: SessionInfo(
                TradingSession.NEW_YORK, time(13, 0), time(22, 0), "UTC",
                volatility_factor=1.1, volume_factor=1.2, spread_factor=0.9
            ),
            TradingSession.OVERLAP_LONDON_NY: SessionInfo(
                TradingSession.OVERLAP_LONDON_NY, time(13, 0), time(17, 0), "UTC",
                volatility_factor=1.3, volume_factor=1.5, spread_factor=0.8
            ),
            TradingSession.OVERLAP_ASIAN_LONDON: SessionInfo(
                TradingSession.OVERLAP_ASIAN_LONDON, time(7, 0), time(9, 0), "UTC",
                volatility_factor=1.1, volume_factor=1.1, spread_factor=0.95
            )
        }

        # Major news times (UTC) - these are typical times for major economic releases
        self.major_news_times = [
            time(8, 30),   # London open news
            time(9, 30),   # European data
            time(12, 30),  # US pre-market
            time(13, 30),  # US market open
            time(14, 30),  # US economic data
            time(18, 0),   # US close
        ]

    def get_current_session(self, current_time: datetime = None) -> TradingSession:
        """Determine current trading session"""
        if current_time is None:
            current_time = datetime.utcnow()

        current_hour = current_time.hour
        current_minute = current_time.minute
        current_time_obj = time(current_hour, current_minute)

        # Check for overlaps first (higher priority)
        if self._is_time_in_session(current_time_obj, self.sessions[TradingSession.OVERLAP_LONDON_NY]):
            return TradingSession.OVERLAP_LONDON_NY
        elif self._is_time_in_session(current_time_obj, self.sessions[TradingSession.OVERLAP_ASIAN_LONDON]):
            return TradingSession.OVERLAP_ASIAN_LONDON

        # Check individual sessions
        for session_type, session_info in self.sessions.items():
            if session_type not in [TradingSession.OVERLAP_LONDON_NY, TradingSession.OVERLAP_ASIAN_LONDON]:
                if self._is_time_in_session(current_time_obj, session_info):
                    return session_type

        return TradingSession.OFF_HOURS

    def _is_time_in_session(self, current_time: time, session: SessionInfo) -> bool:
        """Check if current time is within session hours"""
        if session.start_time <= session.end_time:
            # Normal case: session doesn't cross midnight
            return session.start_time <= current_time <= session.end_time
        else:
            # Session crosses midnight (like Asian session)
            return current_time >= session.start_time or current_time <= session.end_time

    def analyze_market_condition(self,
                                current_time: datetime = None,
                                current_volatility: float = 1.0,
                                current_volume: int = 1000,
                                current_spread: float = 20) -> MarketCondition:
        """
        Analyze current market conditions for trading suitability

        Args:
            current_time: Current time (UTC)
            current_volatility: Current market volatility (ATR-based)
            current_volume: Current volume
            current_spread: Current spread in points

        Returns:
            MarketCondition with analysis results
        """
        if current_time is None:
            current_time = datetime.utcnow()

        # Get current session
        current_session = self.get_current_session(current_time)
        session_info = self.sessions.get(current_session)

        # Check for session overlap
        session_overlap = current_session in [
            TradingSession.OVERLAP_LONDON_NY,
            TradingSession.OVERLAP_ASIAN_LONDON
        ]

        # Analyze volatility
        if session_info:
            expected_volatility = session_info.volatility_factor
            if current_volatility < expected_volatility * 0.7:
                volatility_level = 'low'
            elif current_volatility > expected_volatility * 1.3:
                volatility_level = 'high'
            else:
                volatility_level = 'medium'
        else:
            volatility_level = 'low'  # Off hours typically low volatility

        # Analyze volume
        min_volume = self.config.min_volume_threshold
        if current_volume < min_volume * 0.7:
            volume_level = 'low'
        elif current_volume > min_volume * 1.5:
            volume_level = 'high'
        else:
            volume_level = 'medium'

        # Analyze spread
        if current_spread < 15:
            spread_condition = 'tight'
        elif current_spread > 40:
            spread_condition = 'wide'
        else:
            spread_condition = 'normal'

        # Calculate trading score
        trading_score = self._calculate_trading_score(
            current_session, session_overlap, volatility_level,
            volume_level, spread_condition
        )

        # Generate recommendations
        recommendations = self._generate_timing_recommendations(
            current_session, volatility_level, volume_level, spread_condition, trading_score
        )

        return MarketCondition(
            current_session=current_session,
            session_overlap=session_overlap,
            volatility_level=volatility_level,
            volume_level=volume_level,
            spread_condition=spread_condition,
            trading_score=trading_score,
            recommendations=recommendations
        )

    def _calculate_trading_score(self,
                               session: TradingSession,
                               session_overlap: bool,
                               volatility_level: str,
                               volume_level: str,
                               spread_condition: str) -> float:
        """Calculate overall trading suitability score (0-1)"""
        score = 0.0

        # Session score (40% weight)
        session_scores = {
            TradingSession.OVERLAP_LONDON_NY: 1.0,
            TradingSession.LONDON: 0.9,
            TradingSession.NEW_YORK: 0.85,
            TradingSession.OVERLAP_ASIAN_LONDON: 0.7,
            TradingSession.ASIAN: 0.5,
            TradingSession.OFF_HOURS: 0.2
        }
        score += session_scores.get(session, 0.2) * 0.4

        # Session overlap bonus
        if session_overlap:
            score += self.config.session_overlap_bonus

        # Volatility score (25% weight)
        volatility_scores = {'low': 0.3, 'medium': 1.0, 'high': 0.6}
        score += volatility_scores.get(volatility_level, 0.5) * 0.25

        # Volume score (20% weight)
        volume_scores = {'low': 0.2, 'medium': 0.8, 'high': 1.0}
        score += volume_scores.get(volume_level, 0.5) * 0.2

        # Spread score (15% weight)
        spread_scores = {'tight': 1.0, 'normal': 0.8, 'wide': 0.3}
        score += spread_scores.get(spread_condition, 0.5) * 0.15

        return min(1.0, max(0.0, score))

    def _generate_timing_recommendations(self,
                                       session: TradingSession,
                                       volatility_level: str,
                                       volume_level: str,
                                       spread_condition: str,
                                       trading_score: float) -> List[str]:
        """Generate trading recommendations based on market timing"""
        recommendations = []

        if trading_score > 0.8:
            recommendations.append("Excellent trading conditions - optimal time for active trading")
        elif trading_score > 0.6:
            recommendations.append("Good trading conditions - suitable for trading")
        elif trading_score > 0.4:
            recommendations.append("Fair trading conditions - trade with caution")
        else:
            recommendations.append("Poor trading conditions - consider avoiding new trades")

        # Session-specific recommendations
        if session == TradingSession.OVERLAP_LONDON_NY:
            recommendations.append("London-NY overlap: Expect high volatility and volume")
        elif session == TradingSession.OFF_HOURS:
            recommendations.append("Off-hours trading: Low liquidity, wider spreads expected")
        elif session == TradingSession.ASIAN:
            recommendations.append("Asian session: Lower volatility, range-bound trading likely")

        # Condition-specific recommendations
        if volatility_level == 'high':
            recommendations.append("High volatility: Use wider stops and smaller position sizes")
        elif volatility_level == 'low':
            recommendations.append("Low volatility: Consider tighter stops but expect fewer opportunities")

        if volume_level == 'low':
            recommendations.append("Low volume: Be cautious of false breakouts")

        if spread_condition == 'wide':
            recommendations.append("Wide spreads: Factor in higher transaction costs")

        return recommendations

    def check_news_impact(self, current_time: datetime = None) -> NewsImpact:
        """
        Check for potential news impact on trading

        Args:
            current_time: Current time to check

        Returns:
            NewsImpact analysis
        """
        if current_time is None:
            current_time = datetime.utcnow()

        current_time_obj = current_time.time()

        # Check if we're in news buffer time
        has_major_news = False
        news_time = None
        impact_level = 'low'

        if self.config.avoid_news_hours:
            buffer_minutes = self.config.news_buffer_minutes

            for news_time_obj in self.major_news_times:
                # Create datetime objects for comparison
                news_datetime = datetime.combine(current_time.date(), news_time_obj)
                time_diff = abs((current_time - news_datetime).total_seconds() / 60)

                if time_diff <= buffer_minutes:
                    has_major_news = True
                    news_time = news_datetime

                    # Determine impact level based on time proximity
                    if time_diff <= 15:
                        impact_level = 'high'
                    elif time_diff <= 30:
                        impact_level = 'medium'
                    else:
                        impact_level = 'low'
                    break

        # For gold trading, major currencies that affect XAUUSD
        affected_currencies = ['USD', 'EUR', 'GBP'] if has_major_news else []

        avoid_trading = has_major_news and impact_level in ['high', 'medium']

        return NewsImpact(
            has_major_news=has_major_news,
            news_time=news_time,
            impact_level=impact_level,
            affected_currencies=affected_currencies,
            avoid_trading=avoid_trading
        )

    def get_optimal_trading_windows(self, date: datetime = None) -> List[Dict]:
        """
        Get optimal trading windows for a given date

        Args:
            date: Date to analyze (default: today)

        Returns:
            List of optimal trading windows
        """
        if date is None:
            date = datetime.utcnow().date()

        windows = []

        # London-NY Overlap (best window)
        windows.append({
            'name': 'London-NY Overlap',
            'start_time': time(13, 0),
            'end_time': time(17, 0),
            'score': 1.0,
            'description': 'Highest volatility and volume period',
            'recommended_strategies': ['breakout', 'trend_following', 'scalping']
        })

        # London Session
        windows.append({
            'name': 'London Session',
            'start_time': time(8, 0),
            'end_time': time(13, 0),
            'score': 0.9,
            'description': 'Good volatility, European market active',
            'recommended_strategies': ['trend_following', 'range_trading']
        })

        # NY Session (after overlap)
        windows.append({
            'name': 'NY Session',
            'start_time': time(17, 0),
            'end_time': time(22, 0),
            'score': 0.8,
            'description': 'US market focus, good for USD pairs',
            'recommended_strategies': ['trend_following', 'news_trading']
        })

        # Asian-London Overlap
        windows.append({
            'name': 'Asian-London Overlap',
            'start_time': time(7, 0),
            'end_time': time(9, 0),
            'score': 0.7,
            'description': 'Moderate activity, good for range trading',
            'recommended_strategies': ['range_trading', 'mean_reversion']
        })

        return windows

    def should_trade_now(self,
                        current_time: datetime = None,
                        current_volatility: float = 1.0,
                        current_volume: int = 1000,
                        current_spread: float = 20,
                        min_score_threshold: float = 0.5) -> Tuple[bool, str, float]:
        """
        Determine if current time is suitable for trading

        Args:
            current_time: Current time
            current_volatility: Current volatility level
            current_volume: Current volume
            current_spread: Current spread
            min_score_threshold: Minimum score to allow trading

        Returns:
            Tuple of (should_trade, reason, score)
        """
        # Analyze market condition
        market_condition = self.analyze_market_condition(
            current_time, current_volatility, current_volume, current_spread
        )

        # Check news impact
        news_impact = self.check_news_impact(current_time)

        # Decision logic
        if news_impact.avoid_trading:
            return False, f"Avoiding trading due to {news_impact.impact_level} impact news", 0.0

        if market_condition.trading_score < min_score_threshold:
            return False, f"Market conditions unfavorable (score: {market_condition.trading_score:.2f})", market_condition.trading_score

        # Additional checks
        if market_condition.volume_level == 'low' and self.config.avoid_low_volume_hours:
            return False, "Low volume period - avoiding trades", market_condition.trading_score

        if market_condition.spread_condition == 'wide':
            return False, "Spreads too wide for efficient trading", market_condition.trading_score

        return True, f"Good trading conditions (score: {market_condition.trading_score:.2f})", market_condition.trading_score

    def get_session_statistics(self, historical_data: pd.DataFrame) -> Dict:
        """
        Analyze historical performance by trading session

        Args:
            historical_data: Historical price data with datetime index

        Returns:
            Dictionary with session statistics
        """
        try:
            if historical_data.empty:
                return {}

            session_stats = {}

            for session_type in TradingSession:
                if session_type == TradingSession.OFF_HOURS:
                    continue

                session_data = self._filter_data_by_session(historical_data, session_type)

                if not session_data.empty:
                    # Calculate statistics
                    volatility = session_data['close'].pct_change().std() * np.sqrt(252)
                    avg_volume = session_data.get('volume', pd.Series([1000] * len(session_data))).mean()
                    avg_range = (session_data['high'] - session_data['low']).mean()

                    session_stats[session_type.value] = {
                        'volatility': volatility,
                        'avg_volume': avg_volume,
                        'avg_range': avg_range,
                        'data_points': len(session_data),
                        'best_for': self._get_session_recommendations(session_type)
                    }

            return session_stats

        except Exception as e:
            logger.error(f"Error calculating session statistics: {e}")
            return {}

    def _filter_data_by_session(self, data: pd.DataFrame, session: TradingSession) -> pd.DataFrame:
        """Filter historical data by trading session"""
        try:
            session_info = self.sessions.get(session)
            if not session_info:
                return pd.DataFrame()

            # Filter by hour
            if session_info.start_time <= session_info.end_time:
                mask = (data.index.hour >= session_info.start_time.hour) & \
                       (data.index.hour < session_info.end_time.hour)
            else:
                # Session crosses midnight
                mask = (data.index.hour >= session_info.start_time.hour) | \
                       (data.index.hour < session_info.end_time.hour)

            return data[mask]

        except Exception as e:
            logger.error(f"Error filtering data by session: {e}")
            return pd.DataFrame()

    def _get_session_recommendations(self, session: TradingSession) -> List[str]:
        """Get trading strategy recommendations for each session"""
        recommendations = {
            TradingSession.ASIAN: ['range_trading', 'mean_reversion'],
            TradingSession.LONDON: ['trend_following', 'breakout'],
            TradingSession.NEW_YORK: ['trend_following', 'news_trading'],
            TradingSession.OVERLAP_LONDON_NY: ['scalping', 'breakout', 'trend_following'],
            TradingSession.OVERLAP_ASIAN_LONDON: ['range_trading', 'mean_reversion']
        }

        return recommendations.get(session, ['conservative_trading'])
