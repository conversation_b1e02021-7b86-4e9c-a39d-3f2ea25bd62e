#!/usr/bin/env python3
"""
Backtest Runner for MT5 Trading Bot
Run comprehensive backtests with performance analysis and reporting
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd

# Add src directory to Python path
sys.path.append(str(Path(__file__).parent / "src"))

from src.backtesting.backtest_engine import BacktestEngine, BacktestReporter
from src.core.mt5_client import MT5Client
from src.data.data_manager import DataManager
from src.utils.config import load_config
from src.utils.logger import setup_logger

logger = setup_logger()


async def main():
    """Main backtest runner"""
    print("🤖 MT5 Gold Trading Bot - Backtest Runner")
    print("=" * 60)

    try:
        # Load configuration
        config = load_config()
        logger.info("Configuration loaded")

        # Initialize MT5 client for data collection
        mt5_client = MT5Client(
            config.mt5.login,
            config.mt5.password,
            config.mt5.server,
            symbol=config.mt5.symbol,
            magic_number=config.mt5.magic_number
        )

        # Connect to MT5 (for data collection only)
        if not mt5_client.connect():
            logger.error("Failed to connect to MT5 for data collection")
            print("❌ Cannot connect to MT5. Please ensure:")
            print("   - MT5 terminal is running")
            print("   - Credentials in config.yaml are correct")
            print("   - Internet connection is stable")
            return

        print("✅ Connected to MT5")

        # Initialize data manager
        data_manager = DataManager(mt5_client)

        # Collect historical data if needed
        print("\n📊 Preparing historical data...")

        # Check if we have enough data
        data = data_manager.get_market_data(
            symbol=config.mt5.symbol,
            timeframe=5,  # 5-minute timeframe
            count=40000,  # Large amount for comprehensive backtest
            use_cache=False
        )

        if data is None or len(data) < 1000:
            print("⚠️  Insufficient historical data. Collecting from MT5...")

            # Collect more historical data
            success = data_manager.collect_historical_data(
                symbol=config.mt5.symbol,
                days_back=90  # 3 months of data
            )

            if success:
                print("✅ Historical data collected")
                # Get data again
                data = data_manager.get_market_data(
                    symbol=config.mt5.symbol,
                    timeframe=5,
                    count=40000,
                    use_cache=False
                )
            else:
                print("❌ Failed to collect historical data")
                return

        if data is None or len(data) < 100:
            print("❌ Still insufficient data for backtesting")
            return

        print(f"✅ Data ready: {len(data)} bars from {data.index[0]} to {data.index[-1]}")

        # Set backtest period
        end_date = data.index[-1]
        start_date = end_date - timedelta(days=180)  # Last 60 days

        print(f"\n🔄 Running backtest from {start_date.date()} to {end_date.date()}")

        # Initialize backtest engine
        backtest_engine = BacktestEngine(config)

        # Run backtest
        results = backtest_engine.run_backtest(
            data=data,
            start_date=start_date,
            end_date=end_date
        )

        # Display results
        print("\n" + "=" * 60)
        print("📈 BACKTEST RESULTS")
        print("=" * 60)

        print(f"📅 Period: {results.start_date.date()} to {results.end_date.date()}")
        print(f"💰 Initial Balance: ${results.initial_balance:,.2f}")
        print(f"💰 Final Balance: ${results.final_balance:,.2f}")
        print(f"📊 Total Return: {results.total_return_pct:.2%} (${results.total_return:,.2f})")
        print(f"📉 Max Drawdown: {results.max_drawdown_pct:.2%} (${results.max_drawdown:,.2f})")
        print()
        print(f"🎯 Total Trades: {results.total_trades}")
        print(f"✅ Winning Trades: {results.winning_trades}")
        print(f"❌ Losing Trades: {results.losing_trades}")
        print(f"🏆 Win Rate: {results.win_rate:.1%}")
        print(f"💵 Average Win: ${results.avg_win:.2f}")
        print(f"💸 Average Loss: ${results.avg_loss:.2f}")
        print(f"⚖️  Profit Factor: {results.profit_factor:.2f}")
        print()
        print(f"📊 Sharpe Ratio: {results.sharpe_ratio:.2f}")
        print(f"📊 Sortino Ratio: {results.sortino_ratio:.2f}")
        print(f"📊 Calmar Ratio: {results.calmar_ratio:.2f}")
        print(f"🔥 Max Consecutive Wins: {results.max_consecutive_wins}")
        print(f"❄️  Max Consecutive Losses: {results.max_consecutive_losses}")
        print(f"⏱️  Average Trade Duration: {results.avg_trade_duration:.1f} minutes")

        # Generate detailed report
        print("\n📝 Generating detailed report...")
        reporter = BacktestReporter()

        # Generate HTML report
        report_path = reporter.generate_report(results)
        print(f"✅ HTML report saved: {report_path}")

        # Generate visualizations
        try:
            reporter.create_visualizations(results)
            print("✅ Visualizations created in reports/ directory")
        except Exception as e:
            print(f"⚠️  Could not create visualizations: {e}")
            print("   (This is optional - main results are still valid)")

        # Show recent trades
        if results.trades:
            print(f"\n📋 Last 5 Trades:")
            print("-" * 80)
            print(f"{'Entry Time':<16} {'Dir':<5} {'Entry':<8} {'Exit':<8} {'P&L':<10} {'Duration'}")
            print("-" * 80)

            for trade in results.trades[-5:]:
                direction = trade.direction.upper()
                pnl_str = f"${trade.pnl:+.2f}"
                duration_str = f"{trade.duration_minutes}m"

                print(f"{trade.entry_time.strftime('%m-%d %H:%M'):<16} "
                      f"{direction:<5} "
                      f"{trade.entry_price:<8.5f} "
                      f"{trade.exit_price:<8.5f} "
                      f"{pnl_str:<10} "
                      f"{duration_str}")

        # Performance assessment
        print(f"\n🎯 PERFORMANCE ASSESSMENT:")
        print("-" * 40)

        if results.total_return_pct > 0:
            print("✅ Strategy was profitable")
        else:
            print("❌ Strategy was unprofitable")

        if results.win_rate > 0.5:
            print("✅ Win rate above 50%")
        else:
            print("⚠️  Win rate below 50%")

        if results.profit_factor > 1.5:
            print("✅ Good profit factor (>1.5)")
        elif results.profit_factor > 1.0:
            print("⚠️  Marginal profit factor (1.0-1.5)")
        else:
            print("❌ Poor profit factor (<1.0)")

        if results.max_drawdown_pct < 0.1:
            print("✅ Low drawdown (<10%)")
        elif results.max_drawdown_pct < 0.2:
            print("⚠️  Moderate drawdown (10-20%)")
        else:
            print("❌ High drawdown (>20%)")

        if results.sharpe_ratio > 1.0:
            print("✅ Good risk-adjusted returns (Sharpe > 1.0)")
        elif results.sharpe_ratio > 0.5:
            print("⚠️  Moderate risk-adjusted returns (Sharpe 0.5-1.0)")
        else:
            print("❌ Poor risk-adjusted returns (Sharpe < 0.5)")

        print(f"\n💡 RECOMMENDATIONS:")
        print("-" * 40)

        if results.total_trades < 10:
            print("• Consider longer backtest period for more trades")

        if results.win_rate < 0.4:
            print("• Review entry conditions - win rate is low")

        if results.avg_loss > abs(results.avg_win):
            print("• Consider tighter stop losses or wider take profits")

        if results.max_drawdown_pct > 0.15:
            print("• Reduce position sizes or improve risk management")

        if results.profit_factor < 1.2:
            print("• Strategy needs optimization - profit factor is low")

        print(f"\n📁 Files created:")
        print(f"   • {report_path}")
        print(f"   • reports/equity_curve.png")
        print(f"   • reports/drawdown.png")
        print(f"   • reports/trade_analysis.png")
        print(f"   • reports/monthly_returns.png")
        print(f"   • reports/trading_time_analysis.png")
        print(f"   • reports/hourly_pnl_analysis.png")
        print(f"   • reports/daily_pnl_analysis.png")
        print(f"   • reports/volume_analysis.png")
        print(f"   • reports/volume_performance_analysis.png")
        print(f"   • reports/volume_duration_analysis.png")

        print(f"\n⚠️  IMPORTANT DISCLAIMERS:")
        print("   • Past performance does not guarantee future results")
        print("   • Backtest results may not reflect real trading conditions")
        print("   • Always test strategies in demo mode before live trading")
        print("   • Consider slippage, spreads, and execution delays in live trading")

        # Disconnect from MT5
        mt5_client.disconnect()
        print(f"\n✅ Backtest completed successfully!")

    except Exception as e:
        logger.error(f"Backtest failed: {e}")
        print(f"❌ Backtest failed: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
