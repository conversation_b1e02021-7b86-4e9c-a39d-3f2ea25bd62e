"""
Risk Management System for Gold Trading Bot
Implements position sizing, stop-loss, take-profit, and portfolio risk management
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import MetaTrader5 as mt5

from ..utils.logger import get_logger, TradingLogger
from ..utils.config import RiskConfig, TradingConfig
from ..core.mt5_client import PositionInfo

logger = get_logger(__name__)
trading_logger = TradingLogger("risk_management")


@dataclass
class RiskMetrics:
    """Risk metrics data structure"""
    current_drawdown: float
    max_drawdown: float
    total_risk_exposure: float
    daily_trades_count: int
    win_rate: float
    profit_factor: float
    sharpe_ratio: float
    var_95: float  # Value at Risk 95%


@dataclass
class PositionSize:
    """Position sizing calculation result"""
    volume: float
    risk_amount: float
    stop_loss: float
    take_profit: float
    risk_reward_ratio: float
    confidence_adjusted: bool = False
    volatility_adjusted: bool = False


@dataclass
class AdaptiveRiskParams:
    """Adaptive risk parameters based on market conditions"""
    dynamic_stop_loss: float
    dynamic_take_profit: float
    position_size_multiplier: float
    max_risk_per_trade: float
    volatility_factor: float


class RiskManager:
    """Comprehensive risk management system"""

    def __init__(self, risk_config: RiskConfig, trading_config: TradingConfig):
        self.risk_config = risk_config
        self.trading_config = trading_config
        self.current_balance = 0
        self.peak_balance = 0
        self.daily_trades = {}
        self.trade_history = []
        self.max_positions_violated = False  # Track violation state
        self.total_risk_violated = False  # Track TOTAL_RISK violation state

    def calculate_position_size(self,
                              account_balance: float,
                              entry_price: float,
                              stop_loss: float,
                              atr_value: float,
                              symbol_info: Dict) -> PositionSize:
        """
        Calculate optimal position size based on risk parameters

        Args:
            account_balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Stop loss price
            atr_value: Current ATR value
            symbol_info: Symbol information from MT5

        Returns:
            PositionSize object with calculated values
        """
        try:
            # Calculate risk amount (percentage of balance)
            risk_amount = account_balance * self.trading_config.risk_per_trade

            # Calculate stop loss distance in price
            sl_distance = abs(entry_price - stop_loss)

            # If stop loss is not provided, use ATR-based stop loss
            if sl_distance == 0:
                sl_distance = atr_value * self.risk_config.stop_loss_atr_multiplier
                if entry_price > stop_loss:  # Long position
                    stop_loss = entry_price - sl_distance
                else:  # Short position
                    stop_loss = entry_price + sl_distance

            # Calculate take profit using ATR
            tp_distance = atr_value * self.risk_config.take_profit_atr_multiplier
            if entry_price > stop_loss:  # Long position
                take_profit = entry_price + tp_distance
            else:  # Short position
                take_profit = entry_price - tp_distance

            # Calculate position size
            # Risk Amount = Position Size * Contract Size * Price Distance
            contract_size = symbol_info.get('contract_size', 100)

            # Calculate volume
            # For any symbol, contract size and point value are from symbol info
            # So 1 lot movement of 1 point = $1.00
            # Risk Amount = Volume * Contract Size * Price Distance
            # Volume = Risk Amount / (Contract Size * Price Distance)
            volume = risk_amount / (contract_size * sl_distance)

            trading_logger.log_warning("VOLUME_CALCULATION",
                f"Risk amount: {risk_amount}, Contract size: {contract_size}, SL distance: {sl_distance}, Raw volume: {volume}")

            # Apply volume constraints
            min_volume = symbol_info.get('volume_min', self.trading_config.min_volume)
            max_volume = symbol_info.get('volume_max', self.trading_config.max_volume)
            volume_step = symbol_info.get('volume_step', self.trading_config.volume_step)

            trading_logger.log_warning("VOLUME_CONSTRAINTS",
                f"Min volume: {min_volume}, Max volume: {max_volume}, Volume step: {volume_step}")

            # Round to nearest volume step
            volume = round(volume / volume_step) * volume_step
            volume = max(min_volume, min(volume, max_volume))

            trading_logger.log_warning("VOLUME_FINAL", f"Final volume after constraints: {volume}")

            # Calculate risk-reward ratio
            tp_distance = abs(take_profit - entry_price)
            risk_reward_ratio = tp_distance / sl_distance if sl_distance > 0 else 0

            return PositionSize(
                volume=volume,
                risk_amount=risk_amount,
                stop_loss=stop_loss,
                take_profit=take_profit,
                risk_reward_ratio=risk_reward_ratio
            )

        except Exception as e:
            trading_logger.log_error("POSITION_SIZE_CALCULATION", f"Error calculating position size: {e}")
            logger.error(f"Error calculating position size: {e}")
            return PositionSize(
                volume=min_volume,
                risk_amount=0,
                stop_loss=stop_loss,
                take_profit=entry_price,
                risk_reward_ratio=0
            )

    def calculate_dynamic_position_size(self,
                                       account_balance: float,
                                       entry_price: float,
                                       stop_loss: float,
                                       atr_value: float,
                                       symbol_info: Dict,
                                       signal_confidence: float = 0.5,
                                       volatility_level: str = 'medium',
                                       win_rate: float = 0.5) -> PositionSize:
        """
        Calculate dynamic position size based on confidence, volatility, and performance

        Args:
            account_balance: Current account balance
            entry_price: Planned entry price
            stop_loss: Stop loss price
            atr_value: Current ATR value
            symbol_info: Symbol information from MT5
            signal_confidence: Signal confidence (0-1)
            volatility_level: Market volatility level ('low', 'medium', 'high')
            win_rate: Recent win rate (0-1)

        Returns:
            PositionSize object with dynamic adjustments
        """
        try:
            # Start with base position size
            base_position = self.calculate_position_size(
                account_balance, entry_price, stop_loss, atr_value, symbol_info
            )

            # Dynamic adjustments
            confidence_adjusted = False
            volatility_adjusted = False

            # Confidence-based adjustment
            if self.trading_config.dynamic_sizing_enabled:
                confidence_multiplier = self.trading_config.confidence_multiplier
                if signal_confidence > 0.7:
                    confidence_multiplier *= 1.2  # Increase size for high confidence
                elif signal_confidence < 0.4:
                    confidence_multiplier *= 0.8  # Decrease size for low confidence

                base_position.volume *= confidence_multiplier
                confidence_adjusted = True

            # Volatility-based adjustment
            if self.trading_config.volatility_adjustment:
                if volatility_level == 'low':
                    base_position.volume *= self.trading_config.low_volatility_multiplier
                elif volatility_level == 'high':
                    base_position.volume *= self.trading_config.high_volatility_multiplier
                volatility_adjusted = True

            # Performance-based adjustment
            if win_rate < 0.3:  # Poor recent performance
                base_position.volume *= 0.7
            elif win_rate > 0.7:  # Good recent performance
                base_position.volume *= 1.1

            # Ensure volume limits
            min_volume = self.trading_config.min_volume
            max_volume = self.trading_config.max_volume
            base_position.volume = max(min_volume, min(max_volume, base_position.volume))

            # Round to volume step
            volume_step = self.trading_config.volume_step
            base_position.volume = round(base_position.volume / volume_step) * volume_step

            # Update flags
            base_position.confidence_adjusted = confidence_adjusted
            base_position.volatility_adjusted = volatility_adjusted

            return base_position

        except Exception as e:
            trading_logger.log_error("DYNAMIC_POSITION_SIZE", f"Error calculating dynamic position size: {e}")
            logger.error(f"Error calculating dynamic position size: {e}")
            return self.calculate_position_size(account_balance, entry_price, stop_loss, atr_value, symbol_info)

    def calculate_adaptive_risk_params(self,
                                     atr_value: float,
                                     volatility_level: str,
                                     market_trend: str,
                                     signal_strength: float) -> AdaptiveRiskParams:
        """
        Calculate adaptive risk parameters based on market conditions

        Args:
            atr_value: Current ATR value
            volatility_level: Market volatility level
            market_trend: Market trend direction
            signal_strength: Signal strength (0-1)

        Returns:
            AdaptiveRiskParams with adjusted parameters
        """
        try:
            # Base multipliers
            base_sl_multiplier = self.risk_config.stop_loss_atr_multiplier
            base_tp_multiplier = self.risk_config.take_profit_atr_multiplier

            # Volatility adjustments
            volatility_factor = 1.0
            if volatility_level == 'low':
                # Tighter stops in low volatility
                sl_multiplier = base_sl_multiplier * 0.8
                tp_multiplier = base_tp_multiplier * 0.9
                volatility_factor = 0.9
            elif volatility_level == 'high':
                # Wider stops in high volatility
                sl_multiplier = base_sl_multiplier * 1.3
                tp_multiplier = base_tp_multiplier * 1.2
                volatility_factor = 1.2
            else:
                sl_multiplier = base_sl_multiplier
                tp_multiplier = base_tp_multiplier

            # Trend adjustments
            if market_trend in ['bullish', 'bearish']:
                # Trend following: wider targets, tighter stops
                tp_multiplier *= 1.1
                sl_multiplier *= 0.95

            # Signal strength adjustments
            if signal_strength > 0.8:
                # Strong signals: can afford wider targets
                tp_multiplier *= 1.15
            elif signal_strength < 0.5:
                # Weak signals: tighter targets and stops
                tp_multiplier *= 0.9
                sl_multiplier *= 0.9

            # Position size adjustment
            size_multiplier = 1.0
            if signal_strength > 0.7 and volatility_level == 'low':
                size_multiplier = 1.1
            elif signal_strength < 0.5 or volatility_level == 'high':
                size_multiplier = 0.8

            # Max risk adjustment
            max_risk = self.trading_config.risk_per_trade
            if volatility_level == 'high':
                max_risk *= 0.8  # Reduce risk in high volatility

            return AdaptiveRiskParams(
                dynamic_stop_loss=sl_multiplier * atr_value,
                dynamic_take_profit=tp_multiplier * atr_value,
                position_size_multiplier=size_multiplier,
                max_risk_per_trade=max_risk,
                volatility_factor=volatility_factor
            )

        except Exception as e:
            logger.error(f"Error calculating adaptive risk params: {e}")
            return AdaptiveRiskParams(
                dynamic_stop_loss=base_sl_multiplier * atr_value,
                dynamic_take_profit=base_tp_multiplier * atr_value,
                position_size_multiplier=1.0,
                max_risk_per_trade=self.trading_config.risk_per_trade,
                volatility_factor=1.0
            )

    def check_trading_allowed(self,
                            current_positions: List[PositionInfo],
                            account_info: Dict) -> Tuple[bool, str]:
        """
        Check if new trading is allowed based on risk rules

        Args:
            current_positions: List of current open positions
            account_info: Current account information

        Returns:
            Tuple of (allowed, reason)
        """
        # Update current balance
        self.current_balance = account_info.get('balance', 0)
        self.peak_balance = max(self.peak_balance, self.current_balance)

        # Check maximum positions limit
        if len(current_positions) >= self.trading_config.max_positions:
            reason = f"Maximum positions limit reached ({self.trading_config.max_positions})"
            trading_logger.log_risk_check("MAX_POSITIONS", False, reason, current_positions=len(current_positions), max_positions=self.trading_config.max_positions)
            return False, reason

        # Check daily trades limit
        today = datetime.now().date()
        daily_count = self.daily_trades.get(today, 0)
        if daily_count >= self.trading_config.max_daily_trades:
            reason = f"Daily trades limit reached ({self.trading_config.max_daily_trades})"
            trading_logger.log_risk_check("DAILY_TRADES", False, reason, daily_trades=daily_count, max_daily_trades=self.trading_config.max_daily_trades)
            return False, reason

        # Check maximum drawdown
        current_drawdown = self.calculate_current_drawdown(account_info)
        if current_drawdown > self.risk_config.max_drawdown:
            reason = f"Maximum drawdown exceeded ({current_drawdown:.2%} > {self.risk_config.max_drawdown:.2%})"
            trading_logger.log_risk_check("MAX_DRAWDOWN", False, reason, current_drawdown=f"{current_drawdown:.2%}", max_drawdown=f"{self.risk_config.max_drawdown:.2%}")
            return False, reason

        # Check margin level
        leverage = account_info.get('leverage', 0)
        if leverage < 200:  # Minimum 200% margin level
            reason = f"Insufficient margin level ({leverage:.1f}%)"
            trading_logger.log_risk_check("MARGIN_LEVEL", False, reason, margin_level=f"{leverage:.1f}%", min_required="200%")
            return False, reason

        # Check total risk exposure
        total_risk = self.calculate_total_risk_exposure(current_positions, account_info)
        max_total_risk = self.current_balance * self.risk_config.max_total_risk_exposure
        if total_risk > max_total_risk:
            reason = f"Total risk exposure too high ({total_risk:.2f} > {max_total_risk:.2f})"
            if not self.total_risk_violated:
                trading_logger.log_risk_check("TOTAL_RISK", False, reason, total_risk=f"{total_risk:.2f}", max_risk=f"{max_total_risk:.2f}")
                self.total_risk_violated = True
            else:
                trading_logger.log_risk_check("TOTAL_RISK", False, reason, total_risk=f"{total_risk:.2f}", max_risk=f"{max_total_risk:.2f}", repeat=True)
            return False, reason
        else:
            if self.total_risk_violated:
                # Reset state when back to valid
                self.total_risk_violated = False

        trading_logger.log_risk_check("TRADING_ALLOWED", True, "All risk checks passed")
        return True, "Trading allowed"

    def check_spread_condition(self, symbol_info: Dict) -> bool:
        """Check if spread is within acceptable limits"""
        spread = symbol_info.get('spread', 0)
        return spread <= self.risk_config.max_spread

    def calculate_current_drawdown(self, account_info: Dict) -> float:
        """Calculate current drawdown percentage"""
        current_equity = account_info.get('equity', 0)
        if self.peak_balance == 0:
            return 0.0

        drawdown = (self.peak_balance - current_equity) / self.peak_balance
        return max(0, drawdown)

    def calculate_total_risk_exposure(self,
                                    positions: List[PositionInfo],
                                    account_info: Dict) -> float:
        """Calculate total risk exposure from all positions"""
        total_risk = 0.0

        for position in positions:
            # Estimate risk based on position size and potential loss
            position_value = position.volume * position.price_current * 100  # Assuming contract size 100
            estimated_risk = position_value * 0.02  # Assume 2% risk per position
            total_risk += estimated_risk

        return total_risk

    def analyze_portfolio_correlation(self,
                                    positions: List[PositionInfo],
                                    price_data: Dict[str, pd.DataFrame]) -> Dict:
        """
        Analyze correlation between portfolio positions

        Args:
            positions: List of current positions
            price_data: Historical price data for symbols

        Returns:
            Dictionary with correlation analysis
        """
        try:
            if len(positions) < 2:
                return {'correlation_risk': 'low', 'diversification_score': 1.0}

            # For gold trading, we typically have one symbol (XAUUSD)
            # But we can analyze correlation with different timeframes or position types

            correlation_matrix = {}
            position_types = {'buy': [], 'sell': []}

            # Group positions by type
            for position in positions:
                if position.type == mt5.ORDER_TYPE_BUY:
                    position_types['buy'].append(position)
                else:
                    position_types['sell'].append(position)

            # Calculate correlation risk
            buy_count = len(position_types['buy'])
            sell_count = len(position_types['sell'])
            total_positions = buy_count + sell_count

            # High correlation risk if all positions are in same direction
            if buy_count == total_positions or sell_count == total_positions:
                correlation_risk = 'high'
                diversification_score = 0.3
            elif abs(buy_count - sell_count) <= 1:
                correlation_risk = 'low'
                diversification_score = 1.0
            else:
                correlation_risk = 'medium'
                diversification_score = 0.7

            return {
                'correlation_risk': correlation_risk,
                'diversification_score': diversification_score,
                'buy_positions': buy_count,
                'sell_positions': sell_count,
                'directional_bias': 'bullish' if buy_count > sell_count else 'bearish' if sell_count > buy_count else 'neutral'
            }

        except Exception as e:
            logger.error(f"Error analyzing portfolio correlation: {e}")
            return {'correlation_risk': 'unknown', 'diversification_score': 0.5}

    def generate_portfolio_heat_map(self,
                                  positions: List[PositionInfo],
                                  account_info: Dict) -> Dict:
        """
        Generate portfolio heat map showing risk distribution

        Args:
            positions: List of current positions
            account_info: Account information

        Returns:
            Dictionary with heat map data
        """
        try:
            if not positions:
                return {'total_risk': 0, 'risk_distribution': {}, 'heat_level': 'cold'}

            account_balance = account_info.get('balance', 1)
            total_risk = 0
            position_risks = []

            for position in positions:
                # Calculate position risk
                position_value = abs(position.volume * position.price_current * 100)
                unrealized_pnl = position.profit

                # Estimate maximum potential loss (to stop loss)
                if position.sl > 0:
                    if position.type == mt5.ORDER_TYPE_BUY:
                        max_loss = position.volume * (position.price_current - position.sl) * 100
                    else:
                        max_loss = position.volume * (position.sl - position.price_current) * 100
                else:
                    # Estimate 2% risk if no stop loss
                    max_loss = position_value * 0.02

                risk_percentage = (max_loss / account_balance) * 100
                total_risk += risk_percentage

                position_risks.append({
                    'ticket': position.ticket,
                    'symbol': position.symbol,
                    'type': 'BUY' if position.type == mt5.ORDER_TYPE_BUY else 'SELL',
                    'volume': position.volume,
                    'risk_amount': max_loss,
                    'risk_percentage': risk_percentage,
                    'unrealized_pnl': unrealized_pnl,
                    'heat_level': self._calculate_position_heat(risk_percentage)
                })

            # Determine overall heat level
            if total_risk < 2:
                heat_level = 'cold'
            elif total_risk < 5:
                heat_level = 'warm'
            elif total_risk < 10:
                heat_level = 'hot'
            else:
                heat_level = 'critical'

            # Risk distribution by type
            buy_risk = sum(p['risk_percentage'] for p in position_risks if p['type'] == 'BUY')
            sell_risk = sum(p['risk_percentage'] for p in position_risks if p['type'] == 'SELL')

            return {
                'total_risk_percentage': total_risk,
                'heat_level': heat_level,
                'position_count': len(positions),
                'risk_distribution': {
                    'buy_risk': buy_risk,
                    'sell_risk': sell_risk,
                    'net_risk': buy_risk - sell_risk
                },
                'positions': position_risks,
                'recommendations': self._generate_risk_recommendations(total_risk, heat_level, position_risks)
            }

        except Exception as e:
            logger.error(f"Error generating portfolio heat map: {e}")
            return {'total_risk': 0, 'heat_level': 'unknown', 'error': str(e)}

    def _calculate_position_heat(self, risk_percentage: float) -> str:
        """Calculate heat level for individual position"""
        if risk_percentage < 1:
            return 'cold'
        elif risk_percentage < 2:
            return 'warm'
        elif risk_percentage < 3:
            return 'hot'
        else:
            return 'critical'

    def _generate_risk_recommendations(self,
                                     total_risk: float,
                                     heat_level: str,
                                     position_risks: List[Dict]) -> List[str]:
        """Generate risk management recommendations"""
        recommendations = []

        if heat_level == 'critical':
            recommendations.append("URGENT: Total risk exceeds safe limits. Consider closing some positions.")
        elif heat_level == 'hot':
            recommendations.append("WARNING: High risk exposure. Monitor positions closely.")

        if total_risk > 8:
            recommendations.append("Consider reducing position sizes or implementing tighter stop losses.")

        # Check for individual high-risk positions
        high_risk_positions = [p for p in position_risks if p['risk_percentage'] > 3]
        if high_risk_positions:
            recommendations.append(f"High-risk positions detected: {len(high_risk_positions)} positions with >3% risk each.")

        # Check for concentration risk
        buy_positions = [p for p in position_risks if p['type'] == 'BUY']
        sell_positions = [p for p in position_risks if p['type'] == 'SELL']

        if len(buy_positions) > 3 * len(sell_positions) or len(sell_positions) > 3 * len(buy_positions):
            recommendations.append("High directional concentration. Consider diversifying position types.")

        if not recommendations:
            recommendations.append("Risk levels are within acceptable limits.")

        return recommendations

    def should_close_position(self,
                            position: PositionInfo,
                            current_price: float,
                            atr_value: float) -> Tuple[bool, str]:
        """
        Determine if a position should be closed based on risk rules

        Args:
            position: Position information
            current_price: Current market price
            atr_value: Current ATR value

        Returns:
            Tuple of (should_close, reason)
        """
        # Check trailing stop
        if self.risk_config.trailing_stop:
            if self._check_trailing_stop(position, current_price, atr_value):
                return True, "Trailing stop triggered"

        # Check maximum position age (e.g., 24 hours)
        position_age = datetime.now() - position.time
        if position_age > timedelta(hours=24):
            return True, "Maximum position age exceeded"

        # Check if position is significantly underwater
        unrealized_pnl_pct = position.profit / (position.volume * position.price_open * 100)
        if unrealized_pnl_pct < -0.05:  # More than 5% loss
            return True, "Position significantly underwater"

        return False, "Position within risk parameters"

    def _check_trailing_stop(self,
                           position: PositionInfo,
                           current_price: float,
                           atr_value: float) -> bool:
        """Check if trailing stop should be triggered"""
        trailing_distance = self.risk_config.trailing_stop_distance * 0.00001  # Convert points to price

        if position.type == mt5.ORDER_TYPE_BUY:
            # For long positions, trail below current price
            trailing_stop = current_price - trailing_distance
            return current_price <= trailing_stop
        else:
            # For short positions, trail above current price
            trailing_stop = current_price + trailing_distance
            return current_price >= trailing_stop

    def update_daily_trades(self):
        """Update daily trades counter"""
        today = datetime.now().date()
        self.daily_trades[today] = self.daily_trades.get(today, 0) + 1

        # Clean up old entries (keep only last 7 days)
        cutoff_date = today - timedelta(days=7)
        self.daily_trades = {
            date: count for date, count in self.daily_trades.items()
            if date >= cutoff_date
        }

    def add_trade_to_history(self, trade_data: Dict):
        """Add completed trade to history for analysis"""
        trade_data['timestamp'] = datetime.now()
        self.trade_history.append(trade_data)

        # Keep only last 1000 trades
        if len(self.trade_history) > 1000:
            self.trade_history = self.trade_history[-1000:]

    def calculate_risk_metrics(self, account_info: Dict) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        if not self.trade_history:
            return RiskMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        # Calculate current drawdown
        current_drawdown = self.calculate_current_drawdown(account_info)

        # Calculate maximum drawdown from trade history
        max_drawdown = self._calculate_max_drawdown()

        # Calculate win rate
        winning_trades = sum(1 for trade in self.trade_history if trade.get('profit', 0) > 0)
        win_rate = winning_trades / len(self.trade_history) if self.trade_history else 0

        # Calculate profit factor
        gross_profit = sum(trade.get('profit', 0) for trade in self.trade_history if trade.get('profit', 0) > 0)
        gross_loss = abs(sum(trade.get('profit', 0) for trade in self.trade_history if trade.get('profit', 0) < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0

        # Calculate Sharpe ratio (simplified)
        returns = [trade.get('profit', 0) for trade in self.trade_history]
        if returns:
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            sharpe_ratio = avg_return / std_return if std_return > 0 else 0
        else:
            sharpe_ratio = 0

        # Calculate VaR 95%
        var_95 = np.percentile([trade.get('profit', 0) for trade in self.trade_history], 5) if self.trade_history else 0

        # Get today's trade count
        today = datetime.now().date()
        daily_trades_count = self.daily_trades.get(today, 0)

        return RiskMetrics(
            current_drawdown=current_drawdown,
            max_drawdown=max_drawdown,
            total_risk_exposure=0,  # Will be calculated separately
            daily_trades_count=daily_trades_count,
            win_rate=win_rate,
            profit_factor=profit_factor,
            sharpe_ratio=float(sharpe_ratio),
            var_95=float(var_95)
        )

    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown from trade history"""
        if not self.trade_history:
            return 0.0

        # Calculate cumulative P&L
        cumulative_pnl = []
        running_total = 0
        for trade in self.trade_history:
            running_total += trade.get('profit', 0)
            cumulative_pnl.append(running_total)

        # Calculate maximum drawdown
        peak = cumulative_pnl[0]
        max_dd = 0

        for value in cumulative_pnl:
            if value > peak:
                peak = value
            drawdown = (peak - value) / abs(peak) if peak != 0 else 0
            max_dd = max(max_dd, drawdown)

        return max_dd

    def get_risk_summary(self, account_info: Dict, positions: List[PositionInfo]) -> Dict:
        """Get comprehensive risk summary"""
        metrics = self.calculate_risk_metrics(account_info)
        total_risk = self.calculate_total_risk_exposure(positions, account_info)

        return {
            'current_drawdown': f"{metrics.current_drawdown:.2%}",
            'max_drawdown': f"{metrics.max_drawdown:.2%}",
            'total_risk_exposure': f"${total_risk:.2f}",
            'daily_trades': metrics.daily_trades_count,
            'win_rate': f"{metrics.win_rate:.2%}",
            'profit_factor': f"{metrics.profit_factor:.2f}",
            'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
            'var_95': f"${metrics.var_95:.2f}",
            'account_balance': f"${account_info.get('balance', 0):.2f}",
            'account_equity': f"${account_info.get('equity', 0):.2f}",
            'margin_level': f"{account_info.get('margin_level', 0):.1f}%",
            'open_positions': len(positions)
        }
