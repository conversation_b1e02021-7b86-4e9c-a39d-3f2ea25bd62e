"""
Test script for enhanced trading strategy
Demonstrates the improved features and capabilities
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.config import ConfigManager
from src.strategy.gold_strategy import GoldTradingStrategy, MarketState
from src.indicators.technical_indicators import TechnicalIndicators, MACDSignal, ATRData, PivotPoints, RSIData, BollingerBands, MovingAverages, StochasticData
from src.analysis.market_timing import MarketTimingAnalyzer, TradingSession
from src.analysis.performance_analyzer import PerformanceAnalyzer
from src.risk.risk_manager import RiskManager
from src.core.mt5_client_mock import MockMT5Client


def create_sample_data(days=100):
    """Create sample OHLC data for testing"""
    dates = pd.date_range(start=datetime.now() - timedelta(days=days), periods=days*24*12, freq='5T')
    
    # Generate realistic gold price data
    base_price = 2000.0
    returns = np.random.normal(0, 0.001, len(dates))
    
    # Add some trend and volatility clustering
    for i in range(1, len(returns)):
        returns[i] += 0.1 * returns[i-1]  # Add momentum
    
    prices = base_price * (1 + returns).cumprod()
    
    # Create OHLC data
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    
    # Generate OHLC from close prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.002, len(data)))
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.002, len(data)))
    data['volume'] = np.random.randint(500, 2000, len(data))
    
    return data


def test_enhanced_indicators():
    """Test the enhanced technical indicators"""
    print("=== Testing Enhanced Technical Indicators ===")
    
    # Create sample data
    data = create_sample_data(30)
    
    # Initialize indicators
    config = ConfigManager().get_config()
    indicators = TechnicalIndicators(config)
    
    # Test all indicators
    print("\n1. Testing MACD...")
    macd = indicators.calculate_macd(data)
    print(f"   MACD: {macd.macd:.4f}, Signal: {macd.signal:.4f}, Trend: {macd.trend}")
    
    print("\n2. Testing RSI...")
    rsi = indicators.calculate_rsi(data)
    print(f"   RSI: {rsi.rsi:.2f}, Signal: {rsi.signal}, Divergence: {rsi.divergence}")
    
    print("\n3. Testing Bollinger Bands...")
    bb = indicators.calculate_bollinger_bands(data)
    print(f"   Upper: {bb.upper:.2f}, Middle: {bb.middle:.2f}, Lower: {bb.lower:.2f}")
    print(f"   Position: {bb.position}, Bandwidth: {bb.bandwidth:.2f}")
    
    print("\n4. Testing Moving Averages...")
    ma = indicators.calculate_moving_averages(data)
    print(f"   SMA20: {ma.sma_20:.2f}, SMA50: {ma.sma_50:.2f}")
    print(f"   Trend: {ma.trend}, Crossover: {ma.crossover}")
    
    print("\n5. Testing Stochastic...")
    stoch = indicators.calculate_stochastic(data)
    print(f"   %K: {stoch.k_percent:.2f}, %D: {stoch.d_percent:.2f}")
    print(f"   Signal: {stoch.signal}, Crossover: {stoch.crossover}")
    
    print("\n6. Testing Combined Signal...")
    combined = indicators.get_combined_signal(data)
    print(f"   Signal Strength: {combined['signal_strength']:.3f}")
    print(f"   Recommendation: {combined['recommendation']}")


def test_market_timing():
    """Test market timing analysis"""
    print("\n=== Testing Market Timing Analysis ===")
    
    config = ConfigManager().get_config()
    timing_analyzer = MarketTimingAnalyzer(config.market_timing)
    
    # Test current session
    current_session = timing_analyzer.get_current_session()
    print(f"\n1. Current Trading Session: {current_session.value}")
    
    # Test market condition analysis
    market_condition = timing_analyzer.analyze_market_condition(
        current_time=datetime.now(),
        current_volatility=1.5,
        current_volume=1200,
        current_spread=25
    )
    
    print(f"\n2. Market Condition Analysis:")
    print(f"   Session: {market_condition.current_session.value}")
    print(f"   Session Overlap: {market_condition.session_overlap}")
    print(f"   Volatility Level: {market_condition.volatility_level}")
    print(f"   Volume Level: {market_condition.volume_level}")
    print(f"   Trading Score: {market_condition.trading_score:.3f}")
    
    print(f"\n3. Recommendations:")
    for rec in market_condition.recommendations:
        print(f"   - {rec}")
    
    # Test trading decision
    should_trade, reason, score = timing_analyzer.should_trade_now(
        current_volatility=1.5,
        current_volume=1200,
        current_spread=25
    )
    
    print(f"\n4. Trading Decision:")
    print(f"   Should Trade: {should_trade}")
    print(f"   Reason: {reason}")
    print(f"   Score: {score:.3f}")


def test_enhanced_risk_management():
    """Test enhanced risk management features"""
    print("\n=== Testing Enhanced Risk Management ===")
    
    config = ConfigManager().get_config()
    risk_manager = RiskManager(config.risk, config.trading)
    
    # Test dynamic position sizing
    print("\n1. Testing Dynamic Position Sizing...")
    
    symbol_info = {'contract_size': 100}
    account_balance = 10000
    entry_price = 2000.0
    stop_loss = 1980.0
    atr_value = 15.0
    
    # Standard position size
    standard_pos = risk_manager.calculate_position_size(
        account_balance, entry_price, stop_loss, atr_value, symbol_info
    )
    print(f"   Standard Position Size: {standard_pos.volume:.3f}")
    
    # Dynamic position size with high confidence
    dynamic_pos = risk_manager.calculate_dynamic_position_size(
        account_balance, entry_price, stop_loss, atr_value, symbol_info,
        signal_confidence=0.8, volatility_level='medium', win_rate=0.6
    )
    print(f"   Dynamic Position Size (High Confidence): {dynamic_pos.volume:.3f}")
    print(f"   Confidence Adjusted: {dynamic_pos.confidence_adjusted}")
    print(f"   Volatility Adjusted: {dynamic_pos.volatility_adjusted}")
    
    # Test adaptive risk parameters
    print("\n2. Testing Adaptive Risk Parameters...")
    adaptive_params = risk_manager.calculate_adaptive_risk_params(
        atr_value=15.0,
        volatility_level='high',
        market_trend='bullish',
        signal_strength=0.7
    )
    
    print(f"   Dynamic Stop Loss: {adaptive_params.dynamic_stop_loss:.2f}")
    print(f"   Dynamic Take Profit: {adaptive_params.dynamic_take_profit:.2f}")
    print(f"   Position Size Multiplier: {adaptive_params.position_size_multiplier:.2f}")
    print(f"   Volatility Factor: {adaptive_params.volatility_factor:.2f}")


def test_enhanced_strategy():
    """Test the enhanced trading strategy"""
    print("\n=== Testing Enhanced Trading Strategy ===")
    
    # Load configuration
    config = ConfigManager().get_config()
    
    # Create mock MT5 client
    mt5_client = MockMT5Client("XAUUSD", 234000)
    
    # Initialize strategy
    strategy = GoldTradingStrategy(config, mt5_client)
    
    # Create sample market data
    data = create_sample_data(30)
    
    # Analyze market
    print("\n1. Analyzing Market State...")
    market_state = strategy.analyze_market()
    
    if market_state:
        print(f"   Current Price: {market_state.price:.2f}")
        print(f"   Spread: {market_state.spread:.4f}")
        print(f"   Market Trend: {market_state.market_trend}")
        print(f"   Volatility: {market_state.volatility}")
        
        if market_state.rsi_data:
            print(f"   RSI: {market_state.rsi_data.rsi:.2f} ({market_state.rsi_data.signal})")
        
        if market_state.market_condition:
            print(f"   Trading Score: {market_state.market_condition.trading_score:.3f}")
            print(f"   Current Session: {market_state.market_condition.current_session.value}")
    
    # Test signal generation
    print("\n2. Testing Signal Generation...")
    current_positions = []
    account_info = {'balance': 10000, 'equity': 10000, 'margin': 0}
    
    signal = strategy.generate_signal(market_state, current_positions, account_info)
    
    if signal:
        print(f"   Signal Generated: {signal.action.upper()}")
        print(f"   Strength: {signal.strength:.3f}")
        print(f"   Entry Price: {signal.entry_price:.2f}")
        print(f"   Stop Loss: {signal.stop_loss:.2f}")
        print(f"   Take Profit: {signal.take_profit:.2f}")
        print(f"   Volume: {signal.volume:.3f}")
        print(f"   Reasoning: {signal.reasoning}")
    else:
        print("   No signal generated")


def test_performance_analysis():
    """Test performance analysis capabilities"""
    print("\n=== Testing Performance Analysis ===")
    
    # Create sample performance data
    dates = pd.date_range(start=datetime.now() - timedelta(days=30), periods=30, freq='D')
    returns = pd.Series(np.random.normal(0.001, 0.02, 30), index=dates)
    
    # Create sample trades
    trades = []
    for i in range(20):
        trade = {
            'entry_time': dates[i % len(dates)],
            'exit_time': dates[i % len(dates)] + timedelta(hours=2),
            'direction': 'long' if i % 2 == 0 else 'short',
            'pnl': np.random.normal(10, 50),
            'duration_minutes': np.random.randint(30, 300)
        }
        trades.append(trade)
    
    # Initialize performance analyzer
    analyzer = PerformanceAnalyzer()
    
    # Calculate metrics
    print("\n1. Calculating Performance Metrics...")
    metrics = analyzer.calculate_comprehensive_metrics(returns, trades)
    
    print(f"   Total Return: {metrics.total_return:.2%}")
    print(f"   Annualized Return: {metrics.annualized_return:.2%}")
    print(f"   Sharpe Ratio: {metrics.sharpe_ratio:.2f}")
    print(f"   Max Drawdown: {metrics.max_drawdown:.2%}")
    print(f"   Win Rate: {metrics.win_rate:.2%}")
    print(f"   Profit Factor: {metrics.profit_factor:.2f}")
    
    # Risk analysis
    print("\n2. Risk Analysis...")
    equity_curve = (1 + returns).cumprod() * 10000
    risk_analysis = analyzer.analyze_risk(returns, equity_curve)
    
    print(f"   Downside Deviation: {risk_analysis.downside_deviation:.2%}")
    print(f"   Upside Deviation: {risk_analysis.upside_deviation:.2%}")
    print(f"   Pain Index: {risk_analysis.pain_index:.4f}")
    
    # Generate report
    print("\n3. Generating Performance Report...")
    report = analyzer.generate_performance_report(metrics, risk_analysis, trades, equity_curve)
    
    print("   Report Summary:")
    for key, value in report['summary'].items():
        print(f"     {key}: {value}")
    
    print("\n   Recommendations:")
    for rec in report['recommendations']:
        print(f"     - {rec}")


def main():
    """Main test function"""
    print("🚀 Testing Enhanced MT5 Trading Strategy")
    print("=" * 50)
    
    try:
        # Test individual components
        test_enhanced_indicators()
        test_market_timing()
        test_enhanced_risk_management()
        test_enhanced_strategy()
        test_performance_analysis()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed successfully!")
        print("\n📊 Enhanced Features Summary:")
        print("   ✓ 5 new technical indicators (RSI, BB, MA, Stochastic)")
        print("   ✓ Advanced market timing analysis")
        print("   ✓ Dynamic position sizing")
        print("   ✓ Adaptive risk management")
        print("   ✓ Comprehensive performance analysis")
        print("   ✓ Enhanced signal generation with confirmation")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
