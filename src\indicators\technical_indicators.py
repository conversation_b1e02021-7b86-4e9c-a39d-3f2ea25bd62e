"""
Technical Indicators for Gold Trading
Implements MACD, ATR, and Pivot Points calculations
"""

import pandas as pd
import numpy as np
from typing import Tuple, Dict, Optional
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class MACDSignal:
    """MACD signal data structure"""
    macd: float
    signal: float
    histogram: float
    trend: str  # 'bullish', 'bearish', 'neutral'
    crossover: str  # 'bullish_cross', 'bearish_cross', 'none'


@dataclass
class ATRData:
    """ATR data structure"""
    atr: float
    atr_percentage: float
    volatility_level: str  # 'low', 'medium', 'high'


@dataclass
class PivotPoints:
    """Pivot points data structure"""
    pivot: float
    r1: float
    r2: float
    r3: float
    s1: float
    s2: float
    s3: float
    current_level: str  # 'above_pivot', 'below_pivot', 'at_pivot'


@dataclass
class RSIData:
    """RSI data structure"""
    rsi: float
    signal: str  # 'overbought', 'oversold', 'neutral'
    divergence: str  # 'bullish', 'bearish', 'none'


@dataclass
class BollingerBands:
    """Bollinger Bands data structure"""
    upper: float
    middle: float  # SMA
    lower: float
    bandwidth: float
    position: str  # 'above_upper', 'below_lower', 'middle', 'squeeze'


@dataclass
class MovingAverages:
    """Moving Averages data structure"""
    sma_20: float
    sma_50: float
    ema_20: float
    ema_50: float
    trend: str  # 'bullish', 'bearish', 'neutral'
    crossover: str  # 'golden_cross', 'death_cross', 'none'


@dataclass
class StochasticData:
    """Stochastic oscillator data structure"""
    k_percent: float
    d_percent: float
    signal: str  # 'overbought', 'oversold', 'neutral'
    crossover: str  # 'bullish_cross', 'bearish_cross', 'none'


class TechnicalIndicators:
    """Technical indicators calculator for gold trading"""

    def __init__(self, config=None):
        # Use config values if provided, otherwise use defaults
        if config and hasattr(config, 'indicators'):
            self.macd_fast = config.indicators.macd_fast
            self.macd_slow = config.indicators.macd_slow
            self.macd_signal = config.indicators.macd_signal
            self.atr_period = config.indicators.atr_period
        else:
            # Default values
            self.macd_fast = 12
            self.macd_slow = 26
            self.macd_signal = 9
            self.atr_period = 14

        # Additional indicator periods
        self.rsi_period = 14
        self.bb_period = 20
        self.bb_std = 2
        self.sma_short = 20
        self.sma_long = 50
        self.ema_short = 20
        self.ema_long = 50
        self.stoch_k = 14
        self.stoch_d = 3

    def calculate_macd(self, data: pd.DataFrame,
                      fast_period: Optional[int] = None,
                      slow_period: Optional[int] = None,
                      signal_period: Optional[int] = None) -> MACDSignal:
        """
        Calculate MACD indicator

        Args:
            data: DataFrame with OHLC data
            fast_period: Fast EMA period (default: 12)
            slow_period: Slow EMA period (default: 26)
            signal_period: Signal line EMA period (default: 9)

        Returns:
            MACDSignal object with current MACD values and signals
        """
        if data is None or len(data) < 50:
            logger.warning("Insufficient data for MACD calculation")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

        fast = fast_period or self.macd_fast
        slow = slow_period or self.macd_slow
        signal = signal_period or self.macd_signal

        try:
            # Calculate EMA
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()

            # Calculate MACD line
            macd_line = ema_fast - ema_slow

            # Calculate signal line
            signal_line = macd_line.ewm(span=signal).mean()

            # Calculate histogram
            histogram = macd_line - signal_line

            # Get current values
            current_macd = float(macd_line.iloc[-1]) if len(macd_line) > 0 else 0
            current_signal = float(signal_line.iloc[-1]) if len(signal_line) > 0 else 0
            current_histogram = float(histogram.iloc[-1]) if len(histogram) > 0 else 0

            # Handle NaN values
            current_macd = 0 if pd.isna(current_macd) else current_macd
            current_signal = 0 if pd.isna(current_signal) else current_signal
            current_histogram = 0 if pd.isna(current_histogram) else current_histogram

            # Determine trend
            trend = self._determine_macd_trend(current_macd, current_signal, current_histogram)

            # Detect crossovers
            crossover = self._detect_macd_crossover(macd_line, signal_line)

            return MACDSignal(
                macd=current_macd,
                signal=current_signal,
                histogram=current_histogram,
                trend=trend,
                crossover=crossover
            )

        except Exception as e:
            logger.error(f"Error calculating MACD: {e}")
            return MACDSignal(0, 0, 0, 'neutral', 'none')

    def calculate_atr(self, data: pd.DataFrame, period: Optional[int] = None) -> ATRData:
        """
        Calculate Average True Range (ATR)

        Args:
            data: DataFrame with OHLC data
            period: ATR calculation period (default: 14)

        Returns:
            ATRData object with ATR values and volatility assessment
        """
        if data is None or len(data) < 20:
            logger.warning("Insufficient data for ATR calculation")
            return ATRData(0, 0, 'medium')

        period = period or self.atr_period

        try:
            # Calculate True Range
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)

            # Calculate ATR using EMA
            atr_values = true_range.ewm(span=period).mean()

            current_atr = float(atr_values.iloc[-1]) if len(atr_values) > 0 and not pd.isna(atr_values.iloc[-1]) else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate ATR as percentage of current price
            atr_percentage = (current_atr / current_price) * 100 if current_price > 0 else 0

            # Determine volatility level
            volatility_level = self._assess_volatility(atr_percentage)

            return ATRData(
                atr=current_atr,
                atr_percentage=atr_percentage,
                volatility_level=volatility_level
            )

        except Exception as e:
            logger.error(f"Error calculating ATR: {e}")
            return ATRData(0, 0, 'medium')

    def calculate_pivot_points(self, data: pd.DataFrame) -> PivotPoints:
        """
        Calculate Pivot Points (Standard method)

        Args:
            data: DataFrame with OHLC data

        Returns:
            PivotPoints object with all pivot levels
        """
        if data is None or len(data) < 2:
            logger.warning("Insufficient data for Pivot Points calculation")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

        try:
            # Use previous day's data for pivot calculation
            prev_high = float(data['high'].iloc[-2]) if len(data) > 1 else 0
            prev_low = float(data['low'].iloc[-2]) if len(data) > 1 else 0
            prev_close = float(data['close'].iloc[-2]) if len(data) > 1 else 0
            current_price = float(data['close'].iloc[-1]) if len(data) > 0 else 0

            # Calculate pivot point
            pivot = (prev_high + prev_low + prev_close) / 3

            # Calculate resistance levels
            r1 = 2 * pivot - prev_low
            r2 = pivot + (prev_high - prev_low)
            r3 = prev_high + 2 * (pivot - prev_low)

            # Calculate support levels
            s1 = 2 * pivot - prev_high
            s2 = pivot - (prev_high - prev_low)
            s3 = prev_low - 2 * (prev_high - pivot)

            # Determine current level relative to pivot
            current_level = self._determine_pivot_level(current_price, pivot)

            return PivotPoints(
                pivot=pivot,
                r1=r1, r2=r2, r3=r3,
                s1=s1, s2=s2, s3=s3,
                current_level=current_level
            )

        except Exception as e:
            logger.error(f"Error calculating Pivot Points: {e}")
            return PivotPoints(0, 0, 0, 0, 0, 0, 0, 'at_pivot')

    def calculate_rsi(self, data: pd.DataFrame, period: Optional[int] = None) -> RSIData:
        """
        Calculate RSI (Relative Strength Index)

        Args:
            data: DataFrame with OHLC data
            period: RSI period (default: 14)

        Returns:
            RSIData object with RSI value and signals
        """
        if data is None or len(data) < 30:
            logger.warning("Insufficient data for RSI calculation")
            return RSIData(50, 'neutral', 'none')

        period = period or self.rsi_period

        try:
            # Calculate price changes
            delta = data['close'].diff()

            # Separate gains and losses
            gains = delta.where(delta > 0, 0)
            losses = -delta.where(delta < 0, 0)

            # Calculate average gains and losses
            avg_gains = gains.rolling(window=period).mean()
            avg_losses = losses.rolling(window=period).mean()

            # Calculate RS and RSI
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))

            current_rsi = rsi.iloc[-1]

            # Determine signal
            if current_rsi > 70:
                signal = 'overbought'
            elif current_rsi < 30:
                signal = 'oversold'
            else:
                signal = 'neutral'

            # Check for divergence (simplified)
            divergence = self._check_rsi_divergence(data, rsi)

            return RSIData(current_rsi, signal, divergence)

        except Exception as e:
            logger.error(f"Error calculating RSI: {e}")
            return RSIData(50, 'neutral', 'none')

    def calculate_bollinger_bands(self, data: pd.DataFrame,
                                 period: Optional[int] = None,
                                 std_dev: Optional[float] = None) -> BollingerBands:
        """
        Calculate Bollinger Bands

        Args:
            data: DataFrame with OHLC data
            period: Period for SMA (default: 20)
            std_dev: Standard deviation multiplier (default: 2)

        Returns:
            BollingerBands object with band values and position
        """
        if data is None or len(data) < 30:
            logger.warning("Insufficient data for Bollinger Bands calculation")
            return BollingerBands(0, 0, 0, 0, 'middle')

        period = period or self.bb_period
        std_dev = std_dev or self.bb_std

        try:
            # Calculate SMA (middle band)
            sma = data['close'].rolling(window=period).mean()

            # Calculate standard deviation
            std = data['close'].rolling(window=period).std()

            # Calculate upper and lower bands
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)

            current_price = data['close'].iloc[-1]
            current_upper = upper.iloc[-1]
            current_middle = sma.iloc[-1]
            current_lower = lower.iloc[-1]

            # Calculate bandwidth
            bandwidth = (current_upper - current_lower) / current_middle * 100

            # Determine position
            if current_price > current_upper:
                position = 'above_upper'
            elif current_price < current_lower:
                position = 'below_lower'
            elif bandwidth < 10:  # Squeeze condition
                position = 'squeeze'
            else:
                position = 'middle'

            return BollingerBands(current_upper, current_middle, current_lower, bandwidth, position)

        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {e}")
            return BollingerBands(0, 0, 0, 0, 'middle')

    def calculate_moving_averages(self, data: pd.DataFrame) -> MovingAverages:
        """
        Calculate various moving averages

        Args:
            data: DataFrame with OHLC data

        Returns:
            MovingAverages object with MA values and signals
        """
        if data is None or len(data) < 60:
            logger.warning("Insufficient data for Moving Averages calculation")
            return MovingAverages(0, 0, 0, 0, 'neutral', 'none')

        try:
            # Calculate SMAs
            sma_20 = data['close'].rolling(window=self.sma_short).mean()
            sma_50 = data['close'].rolling(window=self.sma_long).mean()

            # Calculate EMAs
            ema_20 = data['close'].ewm(span=self.ema_short).mean()
            ema_50 = data['close'].ewm(span=self.ema_long).mean()

            current_sma_20 = sma_20.iloc[-1]
            current_sma_50 = sma_50.iloc[-1]
            current_ema_20 = ema_20.iloc[-1]
            current_ema_50 = ema_50.iloc[-1]
            current_price = data['close'].iloc[-1]

            # Determine trend
            if current_sma_20 > current_sma_50 and current_price > current_sma_20:
                trend = 'bullish'
            elif current_sma_20 < current_sma_50 and current_price < current_sma_20:
                trend = 'bearish'
            else:
                trend = 'neutral'

            # Check for crossovers
            prev_sma_20 = sma_20.iloc[-2] if len(sma_20) > 1 else current_sma_20
            prev_sma_50 = sma_50.iloc[-2] if len(sma_50) > 1 else current_sma_50

            if prev_sma_20 <= prev_sma_50 and current_sma_20 > current_sma_50:
                crossover = 'golden_cross'
            elif prev_sma_20 >= prev_sma_50 and current_sma_20 < current_sma_50:
                crossover = 'death_cross'
            else:
                crossover = 'none'

            return MovingAverages(current_sma_20, current_sma_50, current_ema_20, current_ema_50, trend, crossover)

        except Exception as e:
            logger.error(f"Error calculating Moving Averages: {e}")
            return MovingAverages(0, 0, 0, 0, 'neutral', 'none')

    def calculate_stochastic(self, data: pd.DataFrame,
                           k_period: Optional[int] = None,
                           d_period: Optional[int] = None) -> StochasticData:
        """
        Calculate Stochastic Oscillator

        Args:
            data: DataFrame with OHLC data
            k_period: %K period (default: 14)
            d_period: %D period (default: 3)

        Returns:
            StochasticData object with stochastic values and signals
        """
        if data is None or len(data) < 20:
            logger.warning("Insufficient data for Stochastic calculation")
            return StochasticData(50, 50, 'neutral', 'none')

        k_period = k_period or self.stoch_k
        d_period = d_period or self.stoch_d

        try:
            # Calculate %K
            lowest_low = data['low'].rolling(window=k_period).min()
            highest_high = data['high'].rolling(window=k_period).max()

            k_percent = 100 * ((data['close'] - lowest_low) / (highest_high - lowest_low))

            # Calculate %D (SMA of %K)
            d_percent = k_percent.rolling(window=d_period).mean()

            current_k = k_percent.iloc[-1]
            current_d = d_percent.iloc[-1]

            # Determine signal
            if current_k > 80 and current_d > 80:
                signal = 'overbought'
            elif current_k < 20 and current_d < 20:
                signal = 'oversold'
            else:
                signal = 'neutral'

            # Check for crossovers
            prev_k = k_percent.iloc[-2] if len(k_percent) > 1 else current_k
            prev_d = d_percent.iloc[-2] if len(d_percent) > 1 else current_d

            if prev_k <= prev_d and current_k > current_d:
                crossover = 'bullish_cross'
            elif prev_k >= prev_d and current_k < current_d:
                crossover = 'bearish_cross'
            else:
                crossover = 'none'

            return StochasticData(current_k, current_d, signal, crossover)

        except Exception as e:
            logger.error(f"Error calculating Stochastic: {e}")
            return StochasticData(50, 50, 'neutral', 'none')

    def get_combined_signal(self, data: pd.DataFrame) -> Dict:
        """
        Get combined signal from all indicators

        Args:
            data: DataFrame with OHLC data

        Returns:
            Dictionary with all indicator signals and overall assessment
        """
        # Calculate all indicators
        macd_signal = self.calculate_macd(data)
        atr_data = self.calculate_atr(data)
        pivot_points = self.calculate_pivot_points(data)
        rsi_data = self.calculate_rsi(data)
        bb_data = self.calculate_bollinger_bands(data)
        ma_data = self.calculate_moving_averages(data)
        stoch_data = self.calculate_stochastic(data)

        # Calculate enhanced signal strength
        signal_strength = self._calculate_enhanced_signal_strength(
            macd_signal, pivot_points, rsi_data, bb_data, ma_data, stoch_data
        )

        # Determine trading recommendation
        recommendation = self._get_enhanced_trading_recommendation(
            macd_signal, atr_data, pivot_points, rsi_data, bb_data, ma_data, stoch_data, signal_strength
        )

        return {
            'macd': {
                'value': macd_signal.macd,
                'signal': macd_signal.signal,
                'histogram': macd_signal.histogram,
                'trend': macd_signal.trend,
                'crossover': macd_signal.crossover
            },
            'atr': {
                'value': atr_data.atr,
                'percentage': atr_data.atr_percentage,
                'volatility': atr_data.volatility_level
            },
            'pivot_points': {
                'pivot': pivot_points.pivot,
                'resistance': [pivot_points.r1, pivot_points.r2, pivot_points.r3],
                'support': [pivot_points.s1, pivot_points.s2, pivot_points.s3],
                'current_level': pivot_points.current_level
            },
            'rsi': {
                'value': rsi_data.rsi,
                'signal': rsi_data.signal,
                'divergence': rsi_data.divergence
            },
            'bollinger_bands': {
                'upper': bb_data.upper,
                'middle': bb_data.middle,
                'lower': bb_data.lower,
                'bandwidth': bb_data.bandwidth,
                'position': bb_data.position
            },
            'moving_averages': {
                'sma_20': ma_data.sma_20,
                'sma_50': ma_data.sma_50,
                'ema_20': ma_data.ema_20,
                'ema_50': ma_data.ema_50,
                'trend': ma_data.trend,
                'crossover': ma_data.crossover
            },
            'stochastic': {
                'k_percent': stoch_data.k_percent,
                'd_percent': stoch_data.d_percent,
                'signal': stoch_data.signal,
                'crossover': stoch_data.crossover
            },
            'signal_strength': signal_strength,
            'recommendation': recommendation,
            'timestamp': pd.Timestamp.now()
        }

    def _determine_macd_trend(self, macd: float, signal: float, histogram: float) -> str:
        """Determine MACD trend direction"""
        if macd > signal and histogram > 0:
            return 'bullish'
        elif macd < signal and histogram < 0:
            return 'bearish'
        else:
            return 'neutral'

    def _detect_macd_crossover(self, macd_line: pd.Series, signal_line: pd.Series) -> str:
        """Detect MACD crossover signals"""
        # Check if inputs are pandas Series
        if not isinstance(macd_line, pd.Series) or not isinstance(signal_line, pd.Series):
            return 'none'

        if len(macd_line) < 2 or len(signal_line) < 2:
            return 'none'

        # Check for bullish crossover (MACD crosses above signal)
        if (macd_line.iloc[-2] <= signal_line.iloc[-2] and
            macd_line.iloc[-1] > signal_line.iloc[-1]):
            return 'bullish_cross'

        # Check for bearish crossover (MACD crosses below signal)
        elif (macd_line.iloc[-2] >= signal_line.iloc[-2] and
              macd_line.iloc[-1] < signal_line.iloc[-1]):
            return 'bearish_cross'

        return 'none'

    def _assess_volatility(self, atr_percentage: float) -> str:
        """Assess volatility level based on ATR percentage"""
        if atr_percentage < 0.5:
            return 'low'
        elif atr_percentage < 1.0:
            return 'medium'
        else:
            return 'high'

    def _determine_pivot_level(self, current_price: float, pivot: float) -> str:
        """Determine current price level relative to pivot"""
        if current_price > pivot:
            return 'above_pivot'
        elif current_price < pivot:
            return 'below_pivot'
        else:
            return 'at_pivot'

    def _calculate_signal_strength(self, macd_signal: MACDSignal,
                                 pivot_points: PivotPoints) -> float:
        """Calculate overall signal strength"""
        strength = 0.0

        # MACD contribution
        if macd_signal.crossover == 'bullish_cross':
            strength += 0.4
        elif macd_signal.crossover == 'bearish_cross':
            strength -= 0.4
        elif macd_signal.trend == 'bullish':
            strength += 0.2
        elif macd_signal.trend == 'bearish':
            strength -= 0.2

        # Pivot points contribution
        if pivot_points.current_level == 'above_pivot':
            strength += 0.2
        elif pivot_points.current_level == 'below_pivot':
            strength -= 0.2

        return abs(strength)

    def _get_trading_recommendation(self, macd_signal: MACDSignal,
                                  atr_data: ATRData,
                                  pivot_points: PivotPoints,
                                  signal_strength: float) -> str:
        """Get trading recommendation based on all indicators"""
        if signal_strength < 0.3:
            return 'hold'

        if macd_signal.trend == 'bullish' and pivot_points.current_level == 'above_pivot':
            return 'buy'
        elif macd_signal.trend == 'bearish' and pivot_points.current_level == 'below_pivot':
            return 'sell'

        return 'hold'

    def _check_rsi_divergence(self, data: pd.DataFrame, rsi: pd.Series) -> str:
        """Check for RSI divergence (simplified implementation)"""
        try:
            if len(data) < 20 or len(rsi) < 20:
                return 'none'

            # Get recent price and RSI data
            recent_prices = data['close'].tail(10)
            recent_rsi = rsi.tail(10)

            # Simple divergence check: if price makes new high but RSI doesn't
            price_high = recent_prices.max()
            rsi_at_price_high = recent_rsi.iloc[recent_prices.idxmax()]

            if price_high == recent_prices.iloc[-1] and rsi_at_price_high < recent_rsi.max():
                return 'bearish'
            elif recent_prices.min() == recent_prices.iloc[-1] and recent_rsi.iloc[recent_prices.idxmin()] > recent_rsi.min():
                return 'bullish'

            return 'none'
        except:
            return 'none'

    def _calculate_enhanced_signal_strength(self, macd_signal: MACDSignal,
                                          pivot_points: PivotPoints,
                                          rsi_data: RSIData,
                                          bb_data: BollingerBands,
                                          ma_data: MovingAverages,
                                          stoch_data: StochasticData) -> float:
        """Calculate enhanced signal strength using all indicators"""
        strength = 0.0

        # MACD contribution (30%)
        if macd_signal.crossover == 'bullish_cross':
            strength += 0.3
        elif macd_signal.crossover == 'bearish_cross':
            strength -= 0.3
        elif macd_signal.trend == 'bullish':
            strength += 0.15
        elif macd_signal.trend == 'bearish':
            strength -= 0.15

        # RSI contribution (20%)
        if rsi_data.signal == 'oversold':
            strength += 0.2
        elif rsi_data.signal == 'overbought':
            strength -= 0.2
        if rsi_data.divergence == 'bullish':
            strength += 0.1
        elif rsi_data.divergence == 'bearish':
            strength -= 0.1

        # Moving Averages contribution (20%)
        if ma_data.crossover == 'golden_cross':
            strength += 0.2
        elif ma_data.crossover == 'death_cross':
            strength -= 0.2
        elif ma_data.trend == 'bullish':
            strength += 0.1
        elif ma_data.trend == 'bearish':
            strength -= 0.1

        # Bollinger Bands contribution (15%)
        if bb_data.position == 'below_lower':
            strength += 0.15
        elif bb_data.position == 'above_upper':
            strength -= 0.15
        elif bb_data.position == 'squeeze':
            strength += 0.05  # Potential breakout

        # Stochastic contribution (10%)
        if stoch_data.signal == 'oversold' and stoch_data.crossover == 'bullish_cross':
            strength += 0.1
        elif stoch_data.signal == 'overbought' and stoch_data.crossover == 'bearish_cross':
            strength -= 0.1

        # Pivot Points contribution (5%)
        if pivot_points.current_level == 'above_pivot':
            strength += 0.05
        elif pivot_points.current_level == 'below_pivot':
            strength -= 0.05

        return abs(strength)

    def _get_enhanced_trading_recommendation(self, macd_signal: MACDSignal,
                                           atr_data: ATRData,
                                           pivot_points: PivotPoints,
                                           rsi_data: RSIData,
                                           bb_data: BollingerBands,
                                           ma_data: MovingAverages,
                                           stoch_data: StochasticData,
                                           signal_strength: float) -> str:
        """Get enhanced trading recommendation based on all indicators"""
        if signal_strength < 0.4:
            return 'hold'

        # Count bullish and bearish signals
        bullish_signals = 0
        bearish_signals = 0

        # MACD signals
        if macd_signal.trend == 'bullish' or macd_signal.crossover == 'bullish_cross':
            bullish_signals += 1
        elif macd_signal.trend == 'bearish' or macd_signal.crossover == 'bearish_cross':
            bearish_signals += 1

        # RSI signals
        if rsi_data.signal == 'oversold' or rsi_data.divergence == 'bullish':
            bullish_signals += 1
        elif rsi_data.signal == 'overbought' or rsi_data.divergence == 'bearish':
            bearish_signals += 1

        # Moving Average signals
        if ma_data.trend == 'bullish' or ma_data.crossover == 'golden_cross':
            bullish_signals += 1
        elif ma_data.trend == 'bearish' or ma_data.crossover == 'death_cross':
            bearish_signals += 1

        # Bollinger Bands signals
        if bb_data.position == 'below_lower':
            bullish_signals += 1
        elif bb_data.position == 'above_upper':
            bearish_signals += 1

        # Stochastic signals
        if stoch_data.signal == 'oversold' and stoch_data.crossover == 'bullish_cross':
            bullish_signals += 1
        elif stoch_data.signal == 'overbought' and stoch_data.crossover == 'bearish_cross':
            bearish_signals += 1

        # Make decision based on signal consensus
        if bullish_signals >= 3 and bullish_signals > bearish_signals:
            return 'buy'
        elif bearish_signals >= 3 and bearish_signals > bullish_signals:
            return 'sell'
        else:
            return 'hold'
