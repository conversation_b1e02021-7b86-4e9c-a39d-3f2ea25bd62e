"""
Advanced Performance Analysis Module
Provides comprehensive performance metrics and analysis for trading strategies
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics"""
    # Basic metrics
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float

    # Trading metrics
    total_trades: int
    win_rate: float
    profit_factor: float
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float

    # Risk metrics
    var_95: float  # Value at Risk 95%
    cvar_95: float  # Conditional VaR 95%
    beta: float
    alpha: float

    # Advanced metrics
    information_ratio: float
    treynor_ratio: float
    omega_ratio: float
    tail_ratio: float

    # Consistency metrics
    win_streak: int
    loss_streak: int
    monthly_win_rate: float
    consistency_score: float


@dataclass
class RiskAnalysis:
    """Risk analysis results"""
    correlation_with_market: float
    downside_deviation: float
    upside_deviation: float
    tracking_error: float
    maximum_drawdown_duration: int
    recovery_factor: float
    pain_index: float
    ulcer_index: float


class PerformanceAnalyzer:
    """Advanced performance analysis and reporting"""

    def __init__(self, benchmark_data: Optional[pd.DataFrame] = None):
        """
        Initialize performance analyzer

        Args:
            benchmark_data: Benchmark data for comparison (optional)
        """
        self.benchmark_data = benchmark_data

    def calculate_comprehensive_metrics(self,
                                      returns: pd.Series,
                                      trades: List[Dict],
                                      initial_balance: float = 10000) -> PerformanceMetrics:
        """
        Calculate comprehensive performance metrics

        Args:
            returns: Series of returns
            trades: List of trade records
            initial_balance: Initial account balance

        Returns:
            PerformanceMetrics object
        """
        try:
            if returns.empty or len(trades) == 0:
                return self._empty_metrics()

            # Basic calculations
            total_return = (returns + 1).prod() - 1
            annualized_return = self._annualized_return(returns)
            volatility = returns.std() * np.sqrt(252)

            # Risk-adjusted metrics
            sharpe_ratio = self._sharpe_ratio(returns)
            sortino_ratio = self._sortino_ratio(returns)
            max_drawdown = self._max_drawdown(returns)
            calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0

            # Trading metrics
            trade_results = [t.get('pnl', 0) for t in trades]
            winning_trades = [t for t in trade_results if t > 0]
            losing_trades = [t for t in trade_results if t < 0]

            total_trades = len(trades)
            win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0
            profit_factor = sum(winning_trades) / abs(sum(losing_trades)) if losing_trades else float('inf')
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            largest_win = max(winning_trades) if winning_trades else 0
            largest_loss = min(losing_trades) if losing_trades else 0

            # Risk metrics
            var_95 = self._value_at_risk(returns, 0.05)
            cvar_95 = self._conditional_var(returns, 0.05)

            # Beta and Alpha (if benchmark available)
            beta, alpha = self._beta_alpha(returns) if self.benchmark_data is not None else (0, 0)

            # Advanced metrics
            information_ratio = self._information_ratio(returns)
            treynor_ratio = annualized_return / beta if beta != 0 else 0
            omega_ratio = self._omega_ratio(returns)
            tail_ratio = self._tail_ratio(returns)

            # Consistency metrics
            win_streak, loss_streak = self._calculate_streaks(trades)
            monthly_win_rate = self._monthly_win_rate(returns)
            consistency_score = self._consistency_score(returns)

            return PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                total_trades=total_trades,
                win_rate=win_rate,
                profit_factor=profit_factor,
                avg_win=avg_win,
                avg_loss=avg_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                var_95=var_95,
                cvar_95=cvar_95,
                beta=beta,
                alpha=alpha,
                information_ratio=information_ratio,
                treynor_ratio=treynor_ratio,
                omega_ratio=omega_ratio,
                tail_ratio=tail_ratio,
                win_streak=win_streak,
                loss_streak=loss_streak,
                monthly_win_rate=monthly_win_rate,
                consistency_score=consistency_score
            )

        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._empty_metrics()

    def analyze_risk(self, returns: pd.Series, equity_curve: pd.Series) -> RiskAnalysis:
        """
        Perform comprehensive risk analysis

        Args:
            returns: Series of returns
            equity_curve: Equity curve data

        Returns:
            RiskAnalysis object
        """
        try:
            # Market correlation
            correlation_with_market = 0
            if self.benchmark_data is not None:
                benchmark_returns = self.benchmark_data.pct_change().dropna()
                if len(benchmark_returns) == len(returns):
                    correlation_with_market = returns.corr(benchmark_returns)

            # Downside/Upside deviation
            downside_returns = returns[returns < 0]
            upside_returns = returns[returns > 0]
            downside_deviation = downside_returns.std() * np.sqrt(252)
            upside_deviation = upside_returns.std() * np.sqrt(252)

            # Tracking error
            tracking_error = 0
            if self.benchmark_data is not None:
                benchmark_returns = self.benchmark_data.pct_change().dropna()
                if len(benchmark_returns) == len(returns):
                    tracking_error = (returns - benchmark_returns).std() * np.sqrt(252)

            # Drawdown analysis
            drawdown_series = self._drawdown_series(equity_curve)
            max_dd_duration = self._max_drawdown_duration(drawdown_series)
            recovery_factor = abs(returns.sum()) / abs(drawdown_series.min()) if drawdown_series.min() != 0 else 0

            # Pain and Ulcer indices
            pain_index = self._pain_index(drawdown_series)
            ulcer_index = self._ulcer_index(drawdown_series)

            return RiskAnalysis(
                correlation_with_market=correlation_with_market,
                downside_deviation=downside_deviation,
                upside_deviation=upside_deviation,
                tracking_error=tracking_error,
                maximum_drawdown_duration=max_dd_duration,
                recovery_factor=recovery_factor,
                pain_index=pain_index,
                ulcer_index=ulcer_index
            )

        except Exception as e:
            logger.error(f"Error in risk analysis: {e}")
            return RiskAnalysis(0, 0, 0, 0, 0, 0, 0, 0)

    def generate_performance_report(self,
                                  metrics: PerformanceMetrics,
                                  risk_analysis: RiskAnalysis,
                                  trades: List[Dict],
                                  equity_curve: pd.Series) -> Dict:
        """
        Generate comprehensive performance report

        Args:
            metrics: Performance metrics
            risk_analysis: Risk analysis results
            trades: List of trades
            equity_curve: Equity curve data

        Returns:
            Dictionary with complete performance report
        """
        try:
            # Trade analysis
            trade_analysis = self._analyze_trades(trades)

            # Monthly/Weekly performance
            monthly_performance = self._monthly_performance(equity_curve)

            # Drawdown analysis
            drawdown_analysis = self._detailed_drawdown_analysis(equity_curve)

            # Performance attribution
            attribution = self._performance_attribution(trades)

            # Risk-return profile
            risk_return_profile = self._risk_return_profile(metrics, risk_analysis)

            return {
                'summary': {
                    'total_return': f"{metrics.total_return:.2%}",
                    'annualized_return': f"{metrics.annualized_return:.2%}",
                    'volatility': f"{metrics.volatility:.2%}",
                    'sharpe_ratio': f"{metrics.sharpe_ratio:.2f}",
                    'max_drawdown': f"{metrics.max_drawdown:.2%}",
                    'win_rate': f"{metrics.win_rate:.2%}",
                    'profit_factor': f"{metrics.profit_factor:.2f}",
                    'total_trades': metrics.total_trades
                },
                'detailed_metrics': metrics,
                'risk_analysis': risk_analysis,
                'trade_analysis': trade_analysis,
                'monthly_performance': monthly_performance,
                'drawdown_analysis': drawdown_analysis,
                'attribution': attribution,
                'risk_return_profile': risk_return_profile,
                'recommendations': self._generate_recommendations(metrics, risk_analysis)
            }

        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {'error': str(e)}

    def _empty_metrics(self) -> PerformanceMetrics:
        """Return empty metrics for error cases"""
        return PerformanceMetrics(
            total_return=0, annualized_return=0, volatility=0, sharpe_ratio=0,
            sortino_ratio=0, max_drawdown=0, calmar_ratio=0, total_trades=0,
            win_rate=0, profit_factor=0, avg_win=0, avg_loss=0, largest_win=0,
            largest_loss=0, var_95=0, cvar_95=0, beta=0, alpha=0,
            information_ratio=0, treynor_ratio=0, omega_ratio=0, tail_ratio=0,
            win_streak=0, loss_streak=0, monthly_win_rate=0, consistency_score=0
        )

    def _annualized_return(self, returns: pd.Series) -> float:
        """Calculate annualized return"""
        try:
            if len(returns) == 0:
                return 0
            total_return = (returns + 1).prod() - 1
            years = len(returns) / 252  # Assuming daily returns
            return (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
        except:
            return 0

    def _sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        try:
            excess_returns = returns - risk_free_rate / 252
            return excess_returns.mean() / returns.std() * np.sqrt(252) if returns.std() != 0 else 0
        except:
            return 0

    def _sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio"""
        try:
            excess_returns = returns - risk_free_rate / 252
            downside_returns = returns[returns < 0]
            downside_std = downside_returns.std() if len(downside_returns) > 0 else 0
            return excess_returns.mean() / downside_std * np.sqrt(252) if downside_std != 0 else 0
        except:
            return 0

    def _max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown"""
        try:
            cumulative = (1 + returns).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            return drawdown.min()
        except:
            return 0

    def _value_at_risk(self, returns: pd.Series, alpha: float = 0.05) -> float:
        """Calculate Value at Risk"""
        try:
            return returns.quantile(alpha)
        except:
            return 0

    def _conditional_var(self, returns: pd.Series, alpha: float = 0.05) -> float:
        """Calculate Conditional Value at Risk (Expected Shortfall)"""
        try:
            var = self._value_at_risk(returns, alpha)
            return returns[returns <= var].mean()
        except:
            return 0

    def _analyze_trades(self, trades: List[Dict]) -> Dict:
        """Analyze trade patterns and statistics"""
        try:
            if not trades:
                return {}

            # Basic trade statistics
            pnls = [t.get('pnl', 0) for t in trades]
            durations = [t.get('duration_minutes', 0) for t in trades]

            # Trade distribution
            winning_trades = [p for p in pnls if p > 0]
            losing_trades = [p for p in pnls if p < 0]

            # Time analysis
            avg_duration = np.mean(durations) if durations else 0
            avg_winning_duration = np.mean([t.get('duration_minutes', 0) for t in trades if t.get('pnl', 0) > 0])
            avg_losing_duration = np.mean([t.get('duration_minutes', 0) for t in trades if t.get('pnl', 0) < 0])

            return {
                'total_trades': len(trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'avg_duration_minutes': avg_duration,
                'avg_winning_duration': avg_winning_duration if not np.isnan(avg_winning_duration) else 0,
                'avg_losing_duration': avg_losing_duration if not np.isnan(avg_losing_duration) else 0,
                'largest_win': max(pnls) if pnls else 0,
                'largest_loss': min(pnls) if pnls else 0,
                'avg_win_size': np.mean(winning_trades) if winning_trades else 0,
                'avg_loss_size': np.mean(losing_trades) if losing_trades else 0
            }
        except Exception as e:
            logger.error(f"Error analyzing trades: {e}")
            return {}

    def _monthly_performance(self, equity_curve: pd.Series) -> Dict:
        """Calculate monthly performance statistics"""
        try:
            if equity_curve.empty:
                return {}

            # Calculate monthly returns
            monthly_equity = equity_curve.resample('M').last()
            monthly_returns = monthly_equity.pct_change().dropna()

            return {
                'monthly_returns': monthly_returns.to_dict(),
                'best_month': monthly_returns.max(),
                'worst_month': monthly_returns.min(),
                'avg_monthly_return': monthly_returns.mean(),
                'monthly_volatility': monthly_returns.std(),
                'positive_months': (monthly_returns > 0).sum(),
                'negative_months': (monthly_returns < 0).sum(),
                'total_months': len(monthly_returns)
            }
        except Exception as e:
            logger.error(f"Error calculating monthly performance: {e}")
            return {}

    def _detailed_drawdown_analysis(self, equity_curve: pd.Series) -> Dict:
        """Perform detailed drawdown analysis"""
        try:
            drawdown_series = self._drawdown_series(equity_curve)

            # Find all drawdown periods
            in_drawdown = drawdown_series < -0.01  # 1% threshold
            drawdown_periods = []

            start = None
            for i, is_dd in enumerate(in_drawdown):
                if is_dd and start is None:
                    start = i
                elif not is_dd and start is not None:
                    period_dd = drawdown_series.iloc[start:i]
                    drawdown_periods.append({
                        'start_date': equity_curve.index[start],
                        'end_date': equity_curve.index[i-1],
                        'duration_days': i - start,
                        'max_drawdown': period_dd.min(),
                        'recovery_time': 0  # Could be calculated if needed
                    })
                    start = None

            return {
                'max_drawdown': drawdown_series.min(),
                'avg_drawdown': drawdown_series[drawdown_series < 0].mean(),
                'drawdown_periods': len(drawdown_periods),
                'longest_drawdown_days': max([p['duration_days'] for p in drawdown_periods]) if drawdown_periods else 0,
                'deepest_drawdown': min([p['max_drawdown'] for p in drawdown_periods]) if drawdown_periods else 0,
                'time_in_drawdown_pct': (in_drawdown.sum() / len(in_drawdown)) * 100
            }
        except Exception as e:
            logger.error(f"Error in drawdown analysis: {e}")
            return {}

    def _performance_attribution(self, trades: List[Dict]) -> Dict:
        """Analyze performance attribution"""
        try:
            if not trades:
                return {}

            # Group by different attributes
            by_direction = {'long': [], 'short': []}
            by_hour = {}
            by_day_of_week = {}

            for trade in trades:
                pnl = trade.get('pnl', 0)
                direction = trade.get('direction', 'unknown')

                # By direction
                if direction in by_direction:
                    by_direction[direction].append(pnl)

                # By hour (if entry_time available)
                entry_time = trade.get('entry_time')
                if entry_time:
                    if isinstance(entry_time, str):
                        entry_time = datetime.fromisoformat(entry_time)
                    hour = entry_time.hour
                    if hour not in by_hour:
                        by_hour[hour] = []
                    by_hour[hour].append(pnl)

                    # By day of week
                    day = entry_time.weekday()
                    if day not in by_day_of_week:
                        by_day_of_week[day] = []
                    by_day_of_week[day].append(pnl)

            return {
                'by_direction': {k: {'total_pnl': sum(v), 'avg_pnl': np.mean(v), 'count': len(v)}
                               for k, v in by_direction.items() if v},
                'by_hour': {k: {'total_pnl': sum(v), 'avg_pnl': np.mean(v), 'count': len(v)}
                          for k, v in by_hour.items()},
                'by_day_of_week': {k: {'total_pnl': sum(v), 'avg_pnl': np.mean(v), 'count': len(v)}
                                 for k, v in by_day_of_week.items()}
            }
        except Exception as e:
            logger.error(f"Error in performance attribution: {e}")
            return {}

    def _risk_return_profile(self, metrics: PerformanceMetrics, risk_analysis: RiskAnalysis) -> Dict:
        """Create risk-return profile"""
        return {
            'return_volatility_ratio': metrics.annualized_return / metrics.volatility if metrics.volatility != 0 else 0,
            'risk_adjusted_return': metrics.sharpe_ratio,
            'downside_risk': risk_analysis.downside_deviation,
            'upside_potential': risk_analysis.upside_deviation,
            'risk_efficiency': metrics.sortino_ratio,
            'consistency': metrics.consistency_score
        }

    def _generate_recommendations(self, metrics: PerformanceMetrics, risk_analysis: RiskAnalysis) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []

        if metrics.sharpe_ratio < 1.0:
            recommendations.append("Low Sharpe ratio - consider improving risk-adjusted returns")

        if metrics.max_drawdown < -0.20:
            recommendations.append("High maximum drawdown - implement better risk management")

        if metrics.win_rate < 0.40:
            recommendations.append("Low win rate - review entry criteria and signal quality")

        if metrics.profit_factor < 1.5:
            recommendations.append("Low profit factor - optimize exit strategies and risk-reward ratios")

        if risk_analysis.correlation_with_market > 0.8:
            recommendations.append("High market correlation - consider diversification strategies")

        if metrics.consistency_score < 0.6:
            recommendations.append("Low consistency - focus on reducing performance volatility")

        if not recommendations:
            recommendations.append("Performance metrics are within acceptable ranges")

        return recommendations
